#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运营编码格式测试脚本
测试新增的运营编码格式是否能被正确识别
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from core.file_manager import FileManager


def test_operation_code_formats():
    """测试各种运营编码格式是否能被正确识别"""
    print("运营编码格式测试开始...")
    
    # 创建文件管理器实例
    file_manager = FileManager(status_callback=print)
    
    # 测试用例
    test_cases = [
        # 标准格式
        ("GL-123456789-A", True),
        ("ZDDGL-123456789-F", True),
        ("ABC-987654321-B", True),
        ("XYZ-111222333444555-C", True),
        
        # 新增格式 - 字母+数字组合后缀
        ("DLS-10029969879149-A1", True),
        ("DLS-10026890480657-A2", True),
        ("JSB-10026890480657-A2", True),
        ("GL-123456789-B1", True),
        ("ZDDGL-123456789-C2", True),
        
        # 带日期后缀
        ("GL-123456789-A(20250101)", True),
        ("DLS-10029969879149-A1(20250101)", True),
        
        # 无效格式
        ("GL123456789A", False),
        ("GL-123456789", True),  # 这个应该是有效的，因为支持无后缀格式
        ("GL-A-123456789", False),
        ("123456789-GL-A", False),
        ("GL-123-A", False),  # 数字部分太短
        ("GL-123456789-ABC", False),  # 后缀太长
    ]
    
    # 执行测试
    for i, (code, expected) in enumerate(test_cases):
        result = file_manager.is_valid_operation_code_format(code)
        status = "✅ 通过" if result == expected else "❌ 失败"
        print(f"测试 {i+1}: {code} -> 预期: {expected}, 实际: {result} - {status}")
    
    # 测试清理功能
    clean_test_cases = [
        # 原始编码 -> 预期清理结果
        ("商品ID123456:DLS-10029969879149-A1", "DLS-10029969879149-A1"),
        ("DLS-10026890480657-A2:商品ID789012", "DLS-10026890480657-A2"),
        ("JSB-10026890480657-A2 商品ID345678", "JSB-10026890480657-A2"),
        ("商品编码：GL-123456789-B1", "GL-123456789-B1"),
        ("DLS-10029969879149-A1(20250101)-WP", "DLS-10029969879149-A1"),
    ]
    
    print("\n运营编码清理测试:")
    for i, (raw_code, expected) in enumerate(clean_test_cases):
        result = file_manager.clean_operation_code(raw_code)
        status = "✅ 通过" if result == expected else "❌ 失败"
        print(f"清理测试 {i+1}: {raw_code} -> 预期: {expected}, 实际: {result} - {status}")
    
    print("\n运营编码格式测试完成")


if __name__ == "__main__":
    test_operation_code_formats() 