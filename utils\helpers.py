#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块
包含各种辅助函数
"""

import re
import pandas as pd
from pathlib import Path
from functools import lru_cache
import time


def performance_monitor(func):
    """性能监控装饰器"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        if execution_time > 0.1:  # 只记录超过100ms的操作
            print(f"⏱️ {func.__name__} 执行时间: {execution_time:.3f}秒")
        return result
    return wrapper


@lru_cache(maxsize=500)
def clean_sheet_name(name):
    """
    清理工作表名称，确保符合Excel规范
    """
    if not name:
        return "Sheet1"
    
    # 移除或替换不允许的字符
    invalid_chars = ['\\', '/', '*', '?', ':', '[', ']']
    clean_name = str(name)
    
    for char in invalid_chars:
        clean_name = clean_name.replace(char, '_')
    
    # 限制长度（Excel工作表名称最大31个字符）
    if len(clean_name) > 31:
        clean_name = clean_name[:31]
    
    return clean_name


def extract_shop_name(file_name):
    """
    从文件名中提取店铺名称
    规则：取第一个'-'之前的部分作为店铺名称
    例如：抖音GALAKU成人-Jenny 吮吸跳蛋A-GL-3755615211642552348-A（3）7.19 -> 抖音GALAKU成人
    """
    if not file_name:
        return '未知店铺'
    
    # 找到第一个'-'的位置
    first_dash_index = file_name.find('-')
    
    if first_dash_index > 0:
        # 取第一个'-'之前的部分
        shop_name = file_name[:first_dash_index].strip()
        return shop_name if shop_name else '未知店铺'
    else:
        # 如果没有找到'-'，返回整个文件名（去除扩展名）
        return file_name.strip()


@lru_cache(maxsize=1000)
def generate_short_sheet_name(operation_code):
    """
    生成简化的Sheet名称
    规则：
    - ZDDWY-3759694135590781088-A -> 81088-A
    - ZDDWY-3759694135590781088 -> 81088
    - 3759694135590781088 -> 81088 (纯数字取最后5位)
    """
    # 移除手机标记（如果存在）
    if '_手机_' in operation_code:
        base_code = operation_code.split('_手机_')[0]
    else:
        base_code = operation_code

    # 检查是否为纯数字
    if base_code.isdigit():
        # 纯数字情况，取最后5位
        if len(base_code) >= 5:
            return base_code[-5:]
        else:
            return base_code

    # 使用正则表达式匹配运营编码格式
    # 匹配格式：前缀-数字字母混合-字母 或 前缀-数字字母混合
    patterns = [
        r'([A-Z]+)-([A-Z0-9]+)-([A-Z]+)',  # ZDDWY-3759694135590781088-A 或 WYCF-375747170920103964Z-A
        r'([A-Z]+)-([A-Z0-9]+)',           # ZDDWY-3759694135590781088 或 WYCF-375747170920103964Z
    ]

    for pattern in patterns:
        match = re.match(pattern, base_code)
        if match:
            if len(match.groups()) == 3:
                # 有字母后缀的情况：ZDDWY-3759694135590781088-A 或 WYCF-375747170920103964Z-A
                _, middle_part, suffix = match.groups()
                # 取中间部分的最后5个字符 + 字母后缀
                last_5_chars = middle_part[-5:] if len(middle_part) >= 5 else middle_part
                return f"{last_5_chars}-{suffix}"
            elif len(match.groups()) == 2:
                # 没有字母后缀的情况：ZDDWY-3759694135590781088 或 WYCF-375747170920103964Z
                _, middle_part = match.groups()
                # 取中间部分的最后5个字符
                last_5_chars = middle_part[-5:] if len(middle_part) >= 5 else middle_part
                return last_5_chars

    # 如果不匹配标准格式，使用原有的简化逻辑
    if len(base_code) > 25:
        return base_code[:25]
    else:
        return base_code


def get_available_staff(assignment_df):
    """
    从分单表中提取在班客服信息
    """
    staff_list = []
    
    if assignment_df is None or len(assignment_df) == 0:
        return ['默认客服']
    
    # 尝试不同的可能列名
    possible_columns = ['客服', '客服姓名', '姓名', '员工', '员工姓名', '工作人员']
    staff_column = None
    
    for col in possible_columns:
        if col in assignment_df.columns:
            staff_column = assignment_df[col]
            break
    
    # 如果没有找到明确的列名，尝试第一列
    if staff_column is None and len(assignment_df.columns) > 0:
        staff_column = assignment_df.iloc[:, 0]
    
    # 提取客服姓名
    if staff_column is not None:
        for staff in staff_column:
            if pd.notna(staff) and str(staff).strip():
                staff_name = str(staff).strip()
                if staff_name not in staff_list:  # 避免重复
                    staff_list.append(staff_name)
    
    return staff_list if staff_list else ['默认客服']


def extract_codes_from_column(column):
    """从列中提取编码"""
    codes = set()
    for cell in column:
        if pd.notna(cell):
            cell_str = str(cell).strip()

            # 跳过空值和明显的非编码内容
            if not cell_str or cell_str.lower() in ['nan', 'none', '']:
                continue

            # 跳过纯数字（可能是ID但不是我们要的编码）
            if cell_str.isdigit():
                continue

            # 跳过明显的中文描述
            if len(cell_str) > 50:  # 太长的可能是描述
                continue

            # 检查是否包含字母和数字的组合（编码的特征）
            if re.search(r'[A-Z].*\d|[a-z].*\d|\d.*[A-Z]|\d.*[a-z]', cell_str):
                codes.add(cell_str)

    return codes


def merge_summary_data(summary_data):
    """合并相同运营编码的汇总数据，收集所有手机名称"""
    merged_data = {}
    
    for data in summary_data:
        base_code = data['base_operation_code']
        
        if base_code not in merged_data:
            merged_data[base_code] = {
                'sheet_name': data['sheet_name'],
                'base_operation_code': base_code,
                'phone_names': set(),
                'data_count': 0
            }
        
        # 收集手机名称
        phone_name = data['phone_name']
        if phone_name and phone_name != '未分组':
            merged_data[base_code]['phone_names'].add(phone_name)
        
        # 累加数据量
        merged_data[base_code]['data_count'] += data['data_count']
    
    # 转换为列表格式
    result = []
    for base_code, info in merged_data.items():
        # 处理手机名称显示
        if info['phone_names']:
            phone_display = ', '.join(sorted(info['phone_names']))
        else:
            phone_display = '未分组'
        
        result.append({
            'sheet_name': info['sheet_name'],
            'base_operation_code': base_code,
            'phone_name': phone_display,
            'data_count': info['data_count']
        })
    
    return result
