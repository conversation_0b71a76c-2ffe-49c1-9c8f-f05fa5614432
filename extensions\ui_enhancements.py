#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户界面增强模块
提供高级UI组件、主题系统、快捷键支持等
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QSplitter, QTreeWidget, QTreeWidgetItem, QTableWidget,
                            QTableWidgetItem, QTextEdit, QProgressBar, QLabel,
                            QPushButton, QComboBox, QSpinBox, QCheckBox, QSlider,
                            QGroupBox, QFrame, QScrollArea, QDialog, QDialogButtonBox,
                            QMessageBox, QSystemTrayIcon, QMenu, QToolBar,
                            QStatusBar, QDockWidget, QListWidget, QListWidgetItem,
                            QLineEdit, QStackedWidget, QGridLayout)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import (QFont, QPalette, QColor, QLinearGradient, QPainter,
                        QPixmap, QIcon, QKeySequence, QShortcut, QAction,
                        QGraphicsOpacityEffect)
from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import json


@dataclass
class Theme:
    """主题配置"""
    name: str
    primary_color: str
    secondary_color: str
    background_color: str
    text_color: str
    accent_color: str
    success_color: str = "#4CAF50"
    warning_color: str = "#FF9800"
    error_color: str = "#F44336"
    info_color: str = "#2196F3"


class ThemeManager:
    """主题管理器"""
    
    def __init__(self):
        self.themes = {
            'light': Theme(
                name="浅色主题",
                primary_color="#2196F3",
                secondary_color="#FFC107",
                background_color="#FFFFFF",
                text_color="#212121",
                accent_color="#FF5722"
            ),
            'dark': Theme(
                name="深色主题",
                primary_color="#1976D2",
                secondary_color="#FFA000",
                background_color="#121212",
                text_color="#FFFFFF",
                accent_color="#FF5722"
            ),
            'blue': Theme(
                name="蓝色主题",
                primary_color="#1565C0",
                secondary_color="#0277BD",
                background_color="#E3F2FD",
                text_color="#0D47A1",
                accent_color="#FF6F00"
            ),
            'green': Theme(
                name="绿色主题",
                primary_color="#388E3C",
                secondary_color="#689F38",
                background_color="#E8F5E8",
                text_color="#1B5E20",
                accent_color="#FF5722"
            )
        }
        self.current_theme = 'light'
    
    def get_theme(self, theme_name: str = None) -> Theme:
        """获取主题"""
        if theme_name is None:
            theme_name = self.current_theme
        return self.themes.get(theme_name, self.themes['light'])
    
    def set_theme(self, theme_name: str):
        """设置当前主题"""
        if theme_name in self.themes:
            self.current_theme = theme_name
    
    def get_stylesheet(self, theme_name: str = None) -> str:
        """生成样式表"""
        theme = self.get_theme(theme_name)
        
        return f"""
        QMainWindow {{
            background-color: {theme.background_color};
            color: {theme.text_color};
        }}
        
        QPushButton {{
            background-color: {theme.primary_color};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }}
        
        QPushButton:hover {{
            background-color: {theme.secondary_color};
        }}
        
        QPushButton:pressed {{
            background-color: {theme.accent_color};
        }}
        
        QTabWidget::pane {{
            border: 1px solid {theme.primary_color};
            background-color: {theme.background_color};
        }}
        
        QTabBar::tab {{
            background-color: {theme.secondary_color};
            color: {theme.text_color};
            padding: 8px 16px;
            margin-right: 2px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {theme.primary_color};
            color: white;
        }}
        
        QProgressBar {{
            border: 2px solid {theme.primary_color};
            border-radius: 5px;
            text-align: center;
        }}
        
        QProgressBar::chunk {{
            background-color: {theme.primary_color};
            border-radius: 3px;
        }}
        
        QGroupBox {{
            font-weight: bold;
            border: 2px solid {theme.primary_color};
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }}
        """


class AdvancedProgressBar(QWidget):
    """高级进度条"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.current_value = 0
        self.max_value = 100
        self.show_percentage = True
        self.show_eta = True
        self.start_time = None
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 主进度条
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)
        
        # 信息标签
        info_layout = QHBoxLayout()
        self.status_label = QLabel("准备中...")
        self.eta_label = QLabel("")
        self.speed_label = QLabel("")
        
        info_layout.addWidget(self.status_label)
        info_layout.addStretch()
        info_layout.addWidget(self.speed_label)
        info_layout.addWidget(self.eta_label)
        
        layout.addLayout(info_layout)
    
    def set_value(self, value: int, status: str = None):
        """设置进度值"""
        self.current_value = value
        self.progress_bar.setValue(value)
        
        if status:
            self.status_label.setText(status)
        
        if self.start_time:
            self._update_eta()
    
    def set_maximum(self, maximum: int):
        """设置最大值"""
        self.max_value = maximum
        self.progress_bar.setMaximum(maximum)
    
    def start_timing(self):
        """开始计时"""
        self.start_time = datetime.now()
    
    def _update_eta(self):
        """更新预计完成时间"""
        if not self.start_time or self.current_value == 0:
            return
        
        elapsed = (datetime.now() - self.start_time).total_seconds()
        if elapsed > 0:
            speed = self.current_value / elapsed
            remaining = (self.max_value - self.current_value) / speed if speed > 0 else 0
            
            self.speed_label.setText(f"速度: {speed:.1f}/秒")
            
            if remaining > 0:
                eta_minutes = int(remaining // 60)
                eta_seconds = int(remaining % 60)
                self.eta_label.setText(f"预计: {eta_minutes:02d}:{eta_seconds:02d}")


class DataPreviewWidget(QWidget):
    """数据预览组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        self.view_combo = QComboBox()
        self.view_combo.addItems(["表格视图", "树形视图", "文本视图"])
        self.view_combo.currentTextChanged.connect(self.change_view)
        
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("搜索...")
        self.search_box.textChanged.connect(self.filter_data)
        
        toolbar_layout.addWidget(QLabel("视图:"))
        toolbar_layout.addWidget(self.view_combo)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(QLabel("搜索:"))
        toolbar_layout.addWidget(self.search_box)
        
        layout.addLayout(toolbar_layout)
        
        # 数据显示区域
        self.stacked_widget = QStackedWidget()
        
        # 表格视图
        self.table_widget = QTableWidget()
        self.stacked_widget.addWidget(self.table_widget)
        
        # 树形视图
        self.tree_widget = QTreeWidget()
        self.stacked_widget.addWidget(self.tree_widget)
        
        # 文本视图
        self.text_widget = QTextEdit()
        self.text_widget.setReadOnly(True)
        self.stacked_widget.addWidget(self.text_widget)
        
        layout.addWidget(self.stacked_widget)
    
    def load_data(self, data: Any):
        """加载数据"""
        if isinstance(data, pd.DataFrame):
            self._load_dataframe(data)
        elif isinstance(data, dict):
            self._load_dict(data)
        elif isinstance(data, list):
            self._load_list(data)
        else:
            self._load_text(str(data))
    
    def _load_dataframe(self, df: pd.DataFrame):
        """加载DataFrame"""
        # 表格视图
        self.table_widget.setRowCount(len(df))
        self.table_widget.setColumnCount(len(df.columns))
        self.table_widget.setHorizontalHeaderLabels(df.columns.tolist())
        
        for i, row in df.iterrows():
            for j, value in enumerate(row):
                item = QTableWidgetItem(str(value))
                self.table_widget.setItem(i, j, item)
        
        # 文本视图
        self.text_widget.setText(df.to_string())
    
    def _load_dict(self, data: dict):
        """加载字典数据"""
        # 树形视图
        self.tree_widget.clear()
        self._add_dict_to_tree(data, self.tree_widget.invisibleRootItem())
        
        # 文本视图
        self.text_widget.setText(json.dumps(data, ensure_ascii=False, indent=2))
    
    def _load_list(self, data: list):
        """加载列表数据"""
        # 表格视图
        self.table_widget.setRowCount(len(data))
        self.table_widget.setColumnCount(1)
        self.table_widget.setHorizontalHeaderLabels(["值"])
        
        for i, item in enumerate(data):
            table_item = QTableWidgetItem(str(item))
            self.table_widget.setItem(i, 0, table_item)
        
        # 文本视图
        self.text_widget.setText('\n'.join(str(item) for item in data))
    
    def _load_text(self, text: str):
        """加载文本数据"""
        self.text_widget.setText(text)
    
    def _add_dict_to_tree(self, data: dict, parent_item: QTreeWidgetItem):
        """将字典添加到树形视图"""
        for key, value in data.items():
            item = QTreeWidgetItem([str(key), str(value) if not isinstance(value, (dict, list)) else ""])
            parent_item.addChild(item)
            
            if isinstance(value, dict):
                self._add_dict_to_tree(value, item)
            elif isinstance(value, list):
                for i, list_item in enumerate(value):
                    child_item = QTreeWidgetItem([f"[{i}]", str(list_item)])
                    item.addChild(child_item)
    
    def change_view(self, view_name: str):
        """切换视图"""
        view_map = {
            "表格视图": 0,
            "树形视图": 1,
            "文本视图": 2
        }
        index = view_map.get(view_name, 0)
        self.stacked_widget.setCurrentIndex(index)
    
    def filter_data(self, text: str):
        """过滤数据"""
        # 简单的搜索实现
        current_index = self.stacked_widget.currentIndex()
        
        if current_index == 0:  # 表格视图
            for row in range(self.table_widget.rowCount()):
                match = False
                for col in range(self.table_widget.columnCount()):
                    item = self.table_widget.item(row, col)
                    if item and text.lower() in item.text().lower():
                        match = True
                        break
                self.table_widget.setRowHidden(row, not match and text != "")


class StatusDashboard(QWidget):
    """状态仪表板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.metrics = {}
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("系统状态仪表板")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 指标网格
        metrics_layout = QGridLayout()
        
        # CPU使用率
        self.cpu_widget = self.create_metric_widget("CPU使用率", "0%", "#FF5722")
        metrics_layout.addWidget(self.cpu_widget, 0, 0)
        
        # 内存使用率
        self.memory_widget = self.create_metric_widget("内存使用率", "0%", "#2196F3")
        metrics_layout.addWidget(self.memory_widget, 0, 1)
        
        # 处理速度
        self.speed_widget = self.create_metric_widget("处理速度", "0/秒", "#4CAF50")
        metrics_layout.addWidget(self.speed_widget, 1, 0)
        
        # 任务数量
        self.tasks_widget = self.create_metric_widget("活跃任务", "0", "#FF9800")
        metrics_layout.addWidget(self.tasks_widget, 1, 1)
        
        layout.addLayout(metrics_layout)
        
        # 实时图表区域（简化版）
        chart_group = QGroupBox("实时监控")
        chart_layout = QVBoxLayout(chart_group)
        
        self.chart_area = QTextEdit()
        self.chart_area.setMaximumHeight(150)
        self.chart_area.setReadOnly(True)
        chart_layout.addWidget(self.chart_area)
        
        layout.addWidget(chart_group)
    
    def create_metric_widget(self, title: str, value: str, color: str) -> QWidget:
        """创建指标组件"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Shape.Box)
        widget.setStyleSheet(f"""
            QFrame {{
                border: 2px solid {color};
                border-radius: 8px;
                background-color: rgba(255, 255, 255, 0.9);
                padding: 10px;
            }}
        """)
        
        layout = QVBoxLayout(widget)
        
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        value_label.setStyleSheet(f"color: {color};")
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        # 保存值标签的引用
        widget.value_label = value_label
        
        return widget
    
    def update_metrics(self, metrics: Dict[str, Any]):
        """更新指标"""
        self.metrics = metrics
        
        # 更新各个指标
        if 'cpu_usage' in metrics:
            self.cpu_widget.value_label.setText(f"{metrics['cpu_usage']:.1f}%")
        
        if 'memory_usage' in metrics:
            self.memory_widget.value_label.setText(f"{metrics['memory_usage']:.1f}%")
        
        if 'processing_speed' in metrics:
            self.speed_widget.value_label.setText(f"{metrics['processing_speed']:.1f}/秒")
        
        if 'active_tasks' in metrics:
            self.tasks_widget.value_label.setText(str(metrics['active_tasks']))
        
        # 更新图表区域
        self.update_chart()
    
    def update_chart(self):
        """更新图表显示"""
        chart_text = "实时数据:\n"
        for key, value in self.metrics.items():
            chart_text += f"{key}: {value}\n"
        
        self.chart_area.setText(chart_text)


class ShortcutManager:
    """快捷键管理器"""
    
    def __init__(self, parent_widget: QWidget):
        self.parent = parent_widget
        self.shortcuts = {}
        
    def add_shortcut(self, key_sequence: str, callback: Callable, description: str = ""):
        """添加快捷键"""
        shortcut = QShortcut(QKeySequence(key_sequence), self.parent)
        shortcut.activated.connect(callback)
        
        self.shortcuts[key_sequence] = {
            'shortcut': shortcut,
            'callback': callback,
            'description': description
        }
    
    def remove_shortcut(self, key_sequence: str):
        """移除快捷键"""
        if key_sequence in self.shortcuts:
            self.shortcuts[key_sequence]['shortcut'].deleteLater()
            del self.shortcuts[key_sequence]
    
    def get_shortcuts_help(self) -> str:
        """获取快捷键帮助"""
        help_text = "快捷键列表:\n\n"
        for key, info in self.shortcuts.items():
            help_text += f"{key}: {info['description']}\n"
        return help_text


class NotificationSystem:
    """通知系统"""
    
    def __init__(self, parent: QWidget):
        self.parent = parent
        self.tray_icon = None
        self.setup_tray_icon()
        
    def setup_tray_icon(self):
        """设置系统托盘图标"""
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.tray_icon = QSystemTrayIcon(self.parent)
            
            # 创建托盘菜单
            tray_menu = QMenu()
            
            show_action = tray_menu.addAction("显示主窗口")
            show_action.triggered.connect(self.parent.show)

            quit_action = tray_menu.addAction("退出")
            quit_action.triggered.connect(self.parent.close)
            
            self.tray_icon.setContextMenu(tray_menu)
            self.tray_icon.show()
    
    def show_notification(self, title: str, message: str, 
                         icon: QSystemTrayIcon.MessageIcon = QSystemTrayIcon.MessageIcon.Information):
        """显示通知"""
        if self.tray_icon:
            self.tray_icon.showMessage(title, message, icon, 3000)
    
    def show_toast(self, message: str, duration: int = 3000):
        """显示Toast通知"""
        # 简化的Toast实现
        toast = QLabel(message, self.parent)
        toast.setStyleSheet("""
            QLabel {
                background-color: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-size: 12px;
            }
        """)
        toast.setAlignment(Qt.AlignmentFlag.AlignCenter)
        toast.setWindowFlags(Qt.WindowType.ToolTip)
        toast.show()
        
        # 自动隐藏
        QTimer.singleShot(duration, toast.deleteLater)


class AnimationHelper:
    """动画助手"""
    
    @staticmethod
    def fade_in(widget: QWidget, duration: int = 300):
        """淡入动画"""
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        
        animation = QPropertyAnimation(effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(0)
        animation.setEndValue(1)
        animation.start()
        
        return animation
    
    @staticmethod
    def slide_in(widget: QWidget, direction: str = "left", duration: int = 300):
        """滑入动画"""
        geometry = widget.geometry()
        
        if direction == "left":
            start_rect = QRect(-geometry.width(), geometry.y(), geometry.width(), geometry.height())
        elif direction == "right":
            start_rect = QRect(widget.parent().width(), geometry.y(), geometry.width(), geometry.height())
        elif direction == "top":
            start_rect = QRect(geometry.x(), -geometry.height(), geometry.width(), geometry.height())
        else:  # bottom
            start_rect = QRect(geometry.x(), widget.parent().height(), geometry.width(), geometry.height())
        
        widget.setGeometry(start_rect)
        
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(duration)
        animation.setStartValue(start_rect)
        animation.setEndValue(geometry)
        animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        animation.start()
        
        return animation
