#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评价任务拆分工具 - 主入口文件

作者: chen
版本: 2.1
日期: 2025-01-21

功能特性:
- 智能平衡分配算法
- 运营编码合并功能
- 简化Sheet命名规则
- 店铺名称自动提取
- 完整的汇总Sheet功能
- 超链接跳转功能
- 模块化架构设计
- 通用运营编码识别（支持GL、ZDDGL、ZDDGLK、DLS、JSB等任意格式）
- 带日期后缀的运营编码处理
- 支持字母+数字组合后缀（如A1、A2等）
- K列手机名称汇总功能
- 二次匹配功能（支持纯数字匹配，提高匹配成功率）
- 智能列宽自动调整（支持中文字符，优化表格显示效果）

优化特性 (v2.1+):
- 模块化初始化系统
- 集中化配置管理
- 增强的错误处理
- 性能监控和优化
- 资源管理改进
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))


def main():
    """主函数 - 使用新的初始化系统"""
    try:
        # 使用新的应用程序初始化器
        from utils.app_initializer import create_and_run_app
        return create_and_run_app()

    except ImportError:
        # 如果新的初始化器不可用，回退到传统方式
        print("⚠️ 新初始化系统不可用，使用传统启动方式...")
        return main_fallback()

    except Exception as e:
        print(f"❌ 应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


def main_fallback():
    """传统启动方式（备用）"""
    try:
        from PyQt6.QtWidgets import QApplication

        # 基本的日志和性能配置
        try:
            from utils.log_blocker import setup_comprehensive_log_blocking
            from utils.log_config import setup_clean_environment
            from utils.performance_config import optimize_for_large_files

            setup_comprehensive_log_blocking()
            setup_clean_environment()
            optimize_for_large_files()
        except ImportError:
            print("⚠️ 部分优化模块不可用")

        # 创建应用程序
        app = QApplication(sys.argv)
        app.setStyle('Fusion')
        app.setApplicationName("评价任务拆分工具")
        app.setApplicationVersion("2.1")
        app.setOrganizationName("by：chen")

        # 设置图标
        try:
            from utils.resource_manager import setup_app_icon
            setup_app_icon(app)
        except ImportError:
            print("⚠️ 资源管理模块不可用")

        # 创建主窗口
        from gui.main_window import MainWindow
        window = MainWindow()
        window.show()

        # 运行应用程序
        sys.exit(app.exec())

    except Exception as e:
        print(f"❌ 传统启动方式也失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
