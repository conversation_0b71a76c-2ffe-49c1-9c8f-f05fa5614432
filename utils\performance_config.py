#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化配置模块
包含各种性能优化设置和工具
"""

import os
import psutil
import pandas as pd
from pathlib import Path
from functools import wraps

# 导入新的性能监控系统
try:
    from .performance_monitor import performance_monitor, monitor_performance
    PERFORMANCE_MONITORING_AVAILABLE = True
except ImportError:
    PERFORMANCE_MONITORING_AVAILABLE = False

    # 如果性能监控不可用，提供一个空的装饰器
    def monitor_performance(func):
        return func


class PerformanceConfig:
    """性能优化配置类"""
    
    def __init__(self):
        self.memory_threshold = 0.8  # 内存使用阈值
        self.chunk_size = 10000      # 数据块大小
        self.cache_size = 1000       # 缓存大小
        self.enable_monitoring = True # 是否启用性能监控
        
    def get_optimal_chunk_size(self, file_size_mb):
        """根据文件大小获取最优的数据块大小"""
        if file_size_mb < 10:
            return 5000
        elif file_size_mb < 50:
            return 10000
        elif file_size_mb < 100:
            return 20000
        else:
            return 50000
    
    def check_memory_usage(self):
        """检查当前内存使用情况"""
        memory = psutil.virtual_memory()
        return {
            'total': memory.total / (1024**3),  # GB
            'available': memory.available / (1024**3),  # GB
            'percent': memory.percent,
            'warning': memory.percent > self.memory_threshold * 100
        }
    
    def optimize_pandas_settings(self):
        """优化pandas设置以提高性能"""
        # 设置pandas选项以提高性能
        pd.set_option('mode.chained_assignment', None)  # 避免警告
        pd.set_option('compute.use_bottleneck', True)   # 使用bottleneck加速
        pd.set_option('compute.use_numexpr', True)      # 使用numexpr加速
        
    def get_file_size_mb(self, file_path):
        """获取文件大小（MB）"""
        try:
            size_bytes = Path(file_path).stat().st_size
            return size_bytes / (1024 * 1024)
        except:
            return 0
    
    def should_use_chunked_processing(self, file_paths):
        """判断是否应该使用分块处理"""
        total_size = sum(self.get_file_size_mb(fp) for fp in file_paths)
        memory_info = self.check_memory_usage()
        
        # 如果文件总大小超过可用内存的50%，使用分块处理
        return total_size > (memory_info['available'] * 0.5 * 1024)  # 转换为MB
    
    def get_recommended_settings(self, file_paths):
        """获取推荐的性能设置"""
        total_size = sum(self.get_file_size_mb(fp) for fp in file_paths)
        memory_info = self.check_memory_usage()
        
        settings = {
            'use_chunked_processing': self.should_use_chunked_processing(file_paths),
            'chunk_size': self.get_optimal_chunk_size(total_size),
            'enable_caching': total_size < 100,  # 小于100MB时启用缓存
            'memory_warning': memory_info['warning'],
            'total_file_size_mb': total_size,
            'available_memory_gb': memory_info['available']
        }
        
        return settings


# 全局性能配置实例
perf_config = PerformanceConfig()


def optimize_for_large_files():
    """为大文件处理优化设置"""
    perf_config.optimize_pandas_settings()
    
    # 设置环境变量以优化性能
    os.environ['OPENPYXL_OPTIMIZED_WRITER'] = '1'
    os.environ['PANDAS_COPY_ON_WRITE'] = '1'


def get_memory_efficient_excel_engine():
    """获取内存效率最高的Excel引擎"""
    try:
        import xlsxwriter
        return 'xlsxwriter'
    except ImportError:
        try:
            import openpyxl
            return 'openpyxl'
        except ImportError:
            return 'xlwt'


def log_performance_info(operation_name, file_paths=None):
    """记录性能信息"""
    if not perf_config.enable_monitoring:
        return

    memory_info = perf_config.check_memory_usage()
    print(f"🔍 {operation_name} - 性能信息:")
    print(f"  内存使用: {memory_info['percent']:.1f}% ({memory_info['available']:.1f}GB 可用)")

    if file_paths:
        total_size = sum(perf_config.get_file_size_mb(fp) for fp in file_paths)
        print(f"  文件总大小: {total_size:.1f}MB")

        settings = perf_config.get_recommended_settings(file_paths)
        if settings['use_chunked_processing']:
            print(f"  建议使用分块处理，块大小: {settings['chunk_size']}")
        if settings['memory_warning']:
            print(f"  ⚠️ 内存使用率较高，建议关闭其他程序")


# 新的性能监控装饰器，集成了详细的性能分析
def performance_monitor(func):
    """增强的性能监控装饰器"""
    if PERFORMANCE_MONITORING_AVAILABLE:
        return monitor_performance(func)
    else:
        # 如果新的监控系统不可用，使用简单的监控
        @wraps(func)
        def wrapper(*args, **kwargs):
            if perf_config.enable_monitoring:
                import time
                start_time = time.time()
                start_memory = perf_config.check_memory_usage()

                try:
                    result = func(*args, **kwargs)

                    end_time = time.time()
                    end_memory = perf_config.check_memory_usage()
                    execution_time = end_time - start_time

                    print(f"⚡ {func.__name__}: {execution_time:.2f}s, "
                          f"内存: {end_memory['percent']:.1f}%")

                    return result
                except Exception as e:
                    end_time = time.time()
                    execution_time = end_time - start_time
                    print(f"❌ {func.__name__} 失败: {execution_time:.2f}s - {e}")
                    raise
            else:
                return func(*args, **kwargs)

        return wrapper


def get_performance_summary():
    """获取性能摘要"""
    if PERFORMANCE_MONITORING_AVAILABLE:
        return performance_monitor.get_report()
    else:
        memory_info = perf_config.check_memory_usage()
        return {
            'memory_usage': memory_info,
            'monitoring_available': False,
            'message': '详细性能监控不可用，仅提供基本信息'
        }


def print_performance_summary():
    """打印性能摘要"""
    if PERFORMANCE_MONITORING_AVAILABLE:
        try:
            from .performance_monitor import performance_monitor as pm
            pm.print_report()
        except (ImportError, AttributeError):
            # 如果导入失败，使用基本信息
            summary = get_performance_summary()
            print("📊 基本性能信息:")
            print(f"  内存使用: {summary['memory_usage']['percent']:.1f}%")
            print(f"  可用内存: {summary['memory_usage']['available']:.1f}GB")
    else:
        summary = get_performance_summary()
        print("📊 基本性能信息:")
        print(f"  内存使用: {summary['memory_usage']['percent']:.1f}%")
        print(f"  可用内存: {summary['memory_usage']['available']:.1f}GB")
        print(f"  {summary['message']}")
