#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口GUI模块
"""

import sys
import os
import pandas as pd
from pathlib import Path
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QWidget, QPushButton, QLabel, QLineEdit, QTextEdit,
                            QProgressBar, QFileDialog, QMessageBox, QGroupBox, QListWidget,
                            QListWidgetItem, QFrame, QSizePolicy, QSpacerItem)
from PyQt6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QFont, QPalette, QColor, QLinearGradient, QPainter, QPixmap, QIcon

from core.data_processor import DataProcessor
from utils.resource_manager import resource_manager


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.data_processor = None
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("评价任务拆分工具")
        self.setGeometry(100, 100, 850, 850)
        self.setMinimumSize(850, 850)

        # 设置窗口图标
        window_icon = resource_manager.get_app_icon()
        if not window_icon.isNull():
            self.setWindowIcon(window_icon)

        # 设置窗口样式
        self.setStyleSheet(self.get_main_stylesheet())

        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 删除标题区域
        
        # 文件选择区域
        file_group = self.create_file_selection_section()
        main_layout.addWidget(file_group)


        # 控制按钮区域
        button_section = self.create_button_section()
        main_layout.addWidget(button_section)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet(self.get_progress_bar_stylesheet())
        main_layout.addWidget(self.progress_bar)

        # 状态显示区域
        status_section = self.create_status_section()
        main_layout.addWidget(status_section)
        
        # 存储文件路径
        self.assignment_file = ""
        self.daily_file = ""
        self.operation_files = []
        self.output_folder = ""
    
    def select_assignment_file(self):
        """选择客服排班文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择客服排班", "", "Excel文件 (*.xlsx *.xls)")
        if file_path:
            self.assignment_file = file_path
            self.assignment_edit.setText(file_path)
    
    def select_daily_file(self):
        """选择每日工作总表文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择每日工作总表", "", "Excel文件 (*.xlsx *.xls)")
        if file_path:
            self.daily_file = file_path
            self.daily_edit.setText(file_path)
    
    def select_operation_files(self):
        """选择运营表格文件"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择运营表格文件", "", "Excel文件 (*.xlsx *.xls)")
        if file_paths:
            self.operation_files = file_paths
            # 清空列表并添加新文件
            self.operation_list.clear()
            for file_path in file_paths:
                item = QListWidgetItem(os.path.basename(file_path))
                self.operation_list.addItem(item)
    
    def select_output_folder(self):
        """选择输出目录"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if folder_path:
            self.output_folder = folder_path
            self.output_edit.setText(folder_path)
    
    def start_processing(self):
        """开始处理"""
        # 验证输入
        if not self.assignment_file:
            QMessageBox.warning(self, "警告", "请选择客服排班文件")
            return
        
        if not self.daily_file:
            QMessageBox.warning(self, "警告", "请选择每日工作总表文件")
            return
        
        if not self.operation_files:
            QMessageBox.warning(self, "警告", "请选择运营表格文件")
            return
        
        if not self.output_folder:
            QMessageBox.warning(self, "警告", "请选择输出目录")
            return
        
        # 清空状态显示
        self.status_text.clear()
        
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 禁用按钮
        self.start_btn.setEnabled(False)
        self.open_folder_btn.setEnabled(False)
        
        # 创建并启动数据处理器
        self.data_processor = DataProcessor(
            self.assignment_file,
            self.daily_file,
            self.operation_files,
            self.output_folder
        )
        
        # 连接信号
        self.data_processor.progress_updated.connect(self.update_progress)
        self.data_processor.status_updated.connect(self.update_status)
        self.data_processor.finished_signal.connect(self.processing_finished)
        
        # 启动处理
        self.data_processor.start()
    
    def stop_processing(self):
        """停止处理"""
        if self.data_processor and self.data_processor.isRunning():
            self.data_processor.terminate()
            self.data_processor.wait()
            self.update_status("处理已停止")
            self.processing_finished(False, "用户停止了处理")
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def update_status(self, message):
        """更新状态显示"""
        # 添加时间戳
        timestamped_message = f"[{pd.Timestamp.now().strftime('%H:%M:%S')}] {message}"
        self.status_text.append(timestamped_message)
        # 自动滚动到底部
        cursor = self.status_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.status_text.setTextCursor(cursor)
    
    def processing_finished(self, success, message):
        """处理完成"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 恢复按钮状态
        self.start_btn.setEnabled(True)

        # 如果处理成功且有输出文件夹，启用打开文件夹按钮
        if success and self.output_folder and os.path.exists(self.output_folder):
            self.open_folder_btn.setEnabled(True)

        # 显示结果消息
        if success:
            QMessageBox.information(self, "成功", message)
        else:
            QMessageBox.critical(self, "错误", message)

    def open_output_folder(self):
        """打开输出文件夹"""
        if not self.output_folder:
            QMessageBox.warning(self, "警告", "未设置输出文件夹")
            return

        if not os.path.exists(self.output_folder):
            QMessageBox.warning(self, "警告", f"输出文件夹不存在: {self.output_folder}")
            return

        try:
            # 根据操作系统选择合适的命令
            import platform
            system = platform.system()

            if system == "Windows":
                # Windows系统使用explorer
                os.startfile(self.output_folder)
            elif system == "Darwin":  # macOS
                # macOS系统使用open
                os.system(f'open "{self.output_folder}"')
            else:  # Linux和其他Unix系统
                # Linux系统使用xdg-open
                os.system(f'xdg-open "{self.output_folder}"')

            self.update_status(f"已打开输出文件夹: {self.output_folder}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")
            self.update_status(f"打开文件夹失败: {str(e)}")

    def get_main_stylesheet(self):
        """获取主窗口样式表"""
        return """
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f0f4f8, stop:1 #e2e8f0);
                color: #2d3748;
            }

            QWidget {
                font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
                font-size: 10pt;
            }

            QLabel {
                color: #2d3748;
                font-weight: 500;
            }

            QLineEdit {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 12px 16px;
                font-size: 10pt;
                color: #2d3748;
            }

            QLineEdit:focus {
                border-color: #4299e1;
                background-color: #f7fafc;
            }

            QLineEdit:hover {
                border-color: #cbd5e0;
            }

            QTextEdit {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 12px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 9pt;
                color: #2d3748;
                line-height: 1.4;
            }

            QListWidget {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 8px;
                alternate-background-color: #f7fafc;
            }

            QListWidget::item {
                padding: 8px 12px;
                border-radius: 4px;
                margin: 2px 0;
            }

            QListWidget::item:selected {
                background-color: #4299e1;
                color: white;
            }

            QListWidget::item:hover {
                background-color: #e2e8f0;
            }
        """

    def get_progress_bar_stylesheet(self):
        """获取进度条样式表"""
        return """
            QProgressBar {
                background-color: #e2e8f0;
                border: none;
                border-radius: 12px;
                height: 24px;
                text-align: center;
                font-weight: bold;
                color: #2d3748;
            }

            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4299e1, stop:1 #3182ce);
                border-radius: 12px;
                margin: 2px;
            }
        """

    def create_title_section(self):
        """创建标题区域"""
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 16px;
                padding: 15px;
                margin: 5px 0;
                min-height: 80px;
            }
        """)

        title_layout = QVBoxLayout(title_frame)
        title_layout.setSpacing(8)
        title_layout.setContentsMargins(15, 15, 15, 15)

        # 主标题
        main_title = QLabel("📊 评价任务拆分工具")
        main_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18pt;
                font-weight: bold;
                background: transparent;
                padding: 8px 0;
            }
        """)
        main_title.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 副标题
        sub_title = QLabel("智能分配 • 高效处理 • 专业工具")
        sub_title.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 11pt;
                font-weight: 400;
                background: transparent;
                padding: 5px 0;
            }
        """)
        sub_title.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 版本信息
        version_label = QLabel("v2.0")
        version_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 9pt;
                background: transparent;
                padding: 5px 0;
            }
        """)
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        title_layout.addWidget(main_title)
        title_layout.addWidget(sub_title)
        title_layout.addWidget(version_label)

        return title_frame

    def create_file_selection_section(self):
        """创建文件选择区域"""
        file_group = QGroupBox("📁 文件选择")
        file_group.setStyleSheet("""
            QGroupBox {
                font-size: 14pt;
                font-weight: bold;
                color: #2d3748;
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                margin-top: 20px;
                padding-top: 15px;
                background-color: white;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: white;
                color: #4a5568;
            }
        """)

        file_layout = QVBoxLayout(file_group)
        file_layout.setSpacing(15)
        file_layout.setContentsMargins(25, 25, 25, 25)

        # 客服排班选择
        assignment_section = self.create_file_input_row(
            "👥 客服排班:",
            "选择客服排班Excel文件...",
            self.select_assignment_file,
            "#e53e3e"
        )
        self.assignment_edit = assignment_section['edit']
        file_layout.addLayout(assignment_section['layout'])

        # 每日工作总表选择
        daily_section = self.create_file_input_row(
            "📋 每日工作总表:",
            "选择每日工作总表Excel文件...",
            self.select_daily_file,
            "#38a169"
        )
        self.daily_edit = daily_section['edit']
        file_layout.addLayout(daily_section['layout'])

        # 运营表格选择
        operation_layout = QVBoxLayout()
        operation_btn_layout = QHBoxLayout()

        operation_label = QLabel("📊 运营表格:")
        operation_label.setStyleSheet("font-weight: bold; color: #2d3748; font-size: 11pt;")
        operation_btn_layout.addWidget(operation_label)

        operation_btn = QPushButton("🔍 选择运营表格(可多选)")
        operation_btn.clicked.connect(self.select_operation_files)
        operation_btn.setStyleSheet(self.get_file_button_stylesheet("#3182ce"))
        operation_btn_layout.addWidget(operation_btn)

        operation_layout.addLayout(operation_btn_layout)

        # 文件列表显示
        self.operation_list = QListWidget()
        self.operation_list.setMaximumHeight(120)
        self.operation_list.setMinimumHeight(100)
        self.operation_list.setStyleSheet("""
            QListWidget {
                background-color: #f7fafc;
                border: 2px dashed #cbd5e0;
                border-radius: 8px;
                padding: 10px;
                font-size: 9pt;
            }

            QListWidget::item {
                padding: 6px 10px;
                border-radius: 4px;
                margin: 1px 0;
                background-color: white;
                border: 1px solid #e2e8f0;
            }

            QListWidget::item:selected {
                background-color: #4299e1;
                color: white;
                border-color: #3182ce;
            }
        """)
        operation_layout.addWidget(self.operation_list)

        file_layout.addLayout(operation_layout)

        # 输出目录选择
        output_section = self.create_file_input_row(
            "📤 输出目录:",
            "选择输出目录...",
            self.select_output_folder,
            "#805ad5"
        )
        self.output_edit = output_section['edit']
        file_layout.addLayout(output_section['layout'])

        return file_group

    def create_file_input_row(self, label_text, placeholder, callback, color):
        """创建文件输入行"""
        layout = QHBoxLayout()
        layout.setSpacing(10)

        # 标签
        label = QLabel(label_text)
        label.setStyleSheet(f"""
            QLabel {{
                font-weight: bold;
                color: #2d3748;
                font-size: 9pt;
                min-width: 100px;
            }}
        """)
        label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        layout.addWidget(label)

        # 输入框
        edit = QLineEdit()
        edit.setPlaceholderText(placeholder)
        edit.setStyleSheet(f"""
            QLineEdit {{
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 9pt;
                color: #2d3748;
                min-height: 20px;
                max-height: 32px;
            }}

            QLineEdit:focus {{
                border-color: {color};
                background-color: #f7fafc;
            }}

            QLineEdit:hover {{
                border-color: #cbd5e0;
            }}
        """)
        layout.addWidget(edit, 1)

        # 浏览按钮
        btn = QPushButton("📂 浏览")
        btn.clicked.connect(callback)
        btn.setStyleSheet(self.get_file_button_stylesheet(color))
        layout.addWidget(btn)

        return {'layout': layout, 'edit': edit, 'button': btn}

    def get_file_button_stylesheet(self, color):
        """获取文件按钮样式表"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}, stop:1 {self.darken_color(color)});
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-size: 9pt;
                font-weight: bold;
                min-width: 90px;
            }}

            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.lighten_color(color)}, stop:1 {color});
            }}

            QPushButton:pressed {{
                background: {self.darken_color(color)};
            }}
        """

    def darken_color(self, color):
        """使颜色变暗"""
        color_map = {
            "#e53e3e": "#c53030",
            "#38a169": "#2f855a",
            "#3182ce": "#2c5282",
            "#805ad5": "#6b46c1"
        }
        return color_map.get(color, color)

    def lighten_color(self, color):
        """使颜色变亮"""
        color_map = {
            "#e53e3e": "#f56565",
            "#38a169": "#48bb78",
            "#3182ce": "#4299e1",
            "#805ad5": "#9f7aea"
        }
        return color_map.get(color, color)

    def create_button_section(self):
        """创建按钮区域"""
        button_frame = QFrame()
        button_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.8);
                border-radius: 12px;
                padding: 8px;
                margin: 5px 0;
                min-height: 40px;
            }
        """)

        button_layout = QHBoxLayout(button_frame)
        button_layout.setSpacing(15)
        button_layout.setContentsMargins(10, 8, 10, 8)

        # 添加弹性空间
        button_layout.addStretch()

        # 开始拆分按钮
        self.start_btn = QPushButton("🚀 开始拆分")
        self.start_btn.clicked.connect(self.start_processing)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #48bb78, stop:1 #38a169);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 8px 16px;
                font-size: 9pt;
                font-weight: bold;
                min-width: 100px;
            }

            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #68d391, stop:1 #48bb78);
            }

            QPushButton:pressed {
                background: #2f855a;
            }

            QPushButton:disabled {
                background: #e2e8f0;
                color: #a0aec0;
            }
        """)
        button_layout.addWidget(self.start_btn)

        # 打开输出文件夹按钮
        self.open_folder_btn = QPushButton("📁 打开输出文件夹")
        self.open_folder_btn.clicked.connect(self.open_output_folder)
        self.open_folder_btn.setEnabled(False)
        self.open_folder_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4299e1, stop:1 #3182ce);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 8px 16px;
                font-size: 9pt;
                font-weight: bold;
                min-width: 100px;
            }

            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #63b3ed, stop:1 #4299e1);
            }

            QPushButton:pressed {
                background: #2c5282;
            }

            QPushButton:disabled {
                background: #e2e8f0;
                color: #a0aec0;
            }
        """)
        button_layout.addWidget(self.open_folder_btn)

        # 添加弹性空间
        button_layout.addStretch()

        return button_frame

    def create_status_section(self):
        """创建状态显示区域"""
        status_group = QGroupBox("📊 处理状态")
        status_group.setStyleSheet("""
            QGroupBox {
                font-size: 14pt;
                font-weight: bold;
                color: #2d3748;
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                margin-top: 20px;
                padding-top: 15px;
                background-color: white;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: white;
                color: #4a5568;
            }
        """)

        status_layout = QVBoxLayout(status_group)
        status_layout.setContentsMargins(20, 25, 20, 20)

        self.status_text = QTextEdit()
        self.status_text.setReadOnly(True)
        self.status_text.setMaximumHeight(150)
        self.status_text.setMinimumHeight(120)
        self.status_text.setStyleSheet("""
            QTextEdit {
                background-color: #1a202c;
                border: 2px solid #2d3748;
                border-radius: 8px;
                padding: 15px;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 9pt;
                color: #e2e8f0;
                line-height: 1.5;
            }

            QScrollBar:vertical {
                background-color: #2d3748;
                width: 12px;
                border-radius: 6px;
            }

            QScrollBar::handle:vertical {
                background-color: #4a5568;
                border-radius: 6px;
                min-height: 20px;
            }

            QScrollBar::handle:vertical:hover {
                background-color: #718096;
            }
        """)

        # 添加初始欢迎消息
        welcome_msg = """
🎉 欢迎使用评价任务拆分工具-by：小陈

✨ 新功能特性：
• 🚀 智能平衡分配算法
• 🔧 通用运营编码识别
• 📅 日期后缀自动处理
• 🔗 超链接跳转功能

📋 使用步骤：
1. 选择客服排班Excel文件
2. 选择每日工作总表Excel文件
3. 选择运营表格文件(可多选)
4. 选择输出目录
5. 点击"开始拆分"按钮

准备就绪，等待您的操作...
        """
        self.status_text.setPlainText(welcome_msg.strip())

        status_layout.addWidget(self.status_text)

        return status_group
