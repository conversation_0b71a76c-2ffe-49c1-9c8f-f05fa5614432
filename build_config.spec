# -*- mode: python ; coding: utf-8 -*-
"""
PyInstaller 配置文件
用于打包评价任务拆分工具，确保包含所有必要的资源文件
"""

import os
from pathlib import Path

# 获取项目根目录
project_root = Path(os.getcwd())

# 定义需要包含的数据文件
datas = []

# 添加图标和logo文件
icon_files = [
    'favicon.ico',
    'logo.png',
    'logo.jpg', 
    'logo.jpeg',
    'icon.png',
    'app_icon.ico'
]

for icon_file in icon_files:
    icon_path = project_root / icon_file
    if icon_path.exists():
        datas.append((str(icon_path), '.'))

# 添加其他资源文件
resource_files = [
    'README.md',
    'requirements.txt'
]

for resource_file in resource_files:
    resource_path = project_root / resource_file
    if resource_path.exists():
        datas.append((str(resource_path), '.'))

# 隐藏导入的模块
hiddenimports = [
    'openpyxl',
    'pandas',
    'PyQt6',
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'xlsxwriter',
    'psutil'
]

# 排除不需要的模块，减少日志生成
excludes = [
    'matplotlib',
    'tkinter',
    'unittest',
    'test',
    'pydoc',
    'doctest',
    'argparse',
    'logging.handlers',
    'logging.config'
]

# 分析主程序 - 使用清理启动脚本作为入口点
a = Analysis(
    ['clean_start.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 收集所有文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='评价任务拆分工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(project_root / 'favicon.ico') if (project_root / 'favicon.ico').exists() else None,
    version_file=None,
)

# 如果需要创建目录分发版本，取消注释以下代码
# coll = COLLECT(
#     exe,
#     a.binaries,
#     a.zipfiles,
#     a.datas,
#     strip=False,
#     upx=True,
#     upx_exclude=[],
#     name='评价任务拆分工具'
# )
