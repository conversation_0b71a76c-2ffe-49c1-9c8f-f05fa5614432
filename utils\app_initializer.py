#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序初始化模块
负责应用程序的启动和初始化流程
"""

import sys
import traceback
from pathlib import Path
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class AppInitializer:
    """应用程序初始化器"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.config = None
        self.initialization_steps = []
        
    def add_initialization_step(self, step_name: str, step_func, critical: bool = True):
        """添加初始化步骤"""
        self.initialization_steps.append({
            'name': step_name,
            'func': step_func,
            'critical': critical
        })
    
    def setup_default_initialization_steps(self):
        """设置默认的初始化步骤"""
        self.add_initialization_step("加载配置", self._load_config, True)
        self.add_initialization_step("设置环境变量", self._setup_environment, True)
        self.add_initialization_step("配置日志阻止", self._setup_log_blocking, False)
        self.add_initialization_step("优化性能设置", self._optimize_performance, False)
        self.add_initialization_step("创建应用程序", self._create_application, True)
        self.add_initialization_step("设置应用程序信息", self._setup_app_info, False)
        self.add_initialization_step("设置应用程序图标", self._setup_app_icon, False)
        self.add_initialization_step("记录资源信息", self._log_resource_info, False)
        self.add_initialization_step("创建主窗口", self._create_main_window, True)
    
    def _load_config(self):
        """加载配置"""
        try:
            from config.app_config import get_config
            self.config = get_config()
            print("✅ 配置加载成功")
            return True
        except Exception as e:
            print(f"❌ 配置加载失败: {e}")
            return False
    
    def _setup_environment(self):
        """设置环境变量"""
        try:
            if self.config:
                self.config.apply_environment_variables()
                print("✅ 环境变量设置成功")
            return True
        except Exception as e:
            print(f"❌ 环境变量设置失败: {e}")
            return False
    
    def _setup_log_blocking(self):
        """设置日志阻止"""
        try:
            from utils.log_blocker import setup_comprehensive_log_blocking
            from utils.log_config import setup_clean_environment
            
            setup_comprehensive_log_blocking()
            setup_clean_environment()
            print("✅ 日志阻止设置成功")
            return True
        except Exception as e:
            print(f"⚠️ 日志阻止设置失败: {e}")
            return False
    
    def _optimize_performance(self):
        """优化性能设置"""
        try:
            from utils.performance_config import optimize_for_large_files
            optimize_for_large_files()
            print("✅ 性能优化设置成功")
            return True
        except Exception as e:
            print(f"⚠️ 性能优化设置失败: {e}")
            return False
    
    def _create_application(self):
        """创建应用程序"""
        try:
            self.app = QApplication(sys.argv)
            if self.config:
                self.app.setStyle(self.config.APP_STYLE)
            print("✅ 应用程序创建成功")
            return True
        except Exception as e:
            print(f"❌ 应用程序创建失败: {e}")
            return False
    
    def _setup_app_info(self):
        """设置应用程序信息"""
        try:
            if self.app and self.config:
                self.app.setApplicationName(self.config.APP_NAME)
                self.app.setApplicationVersion(self.config.APP_VERSION)
                self.app.setOrganizationName(self.config.APP_AUTHOR)
                print("✅ 应用程序信息设置成功")
            return True
        except Exception as e:
            print(f"⚠️ 应用程序信息设置失败: {e}")
            return False
    
    def _setup_app_icon(self):
        """设置应用程序图标"""
        try:
            from utils.resource_manager import setup_app_icon
            if self.app:
                icon_set = setup_app_icon(self.app)
                if icon_set:
                    print("✅ 应用程序图标设置成功")
                else:
                    print("⚠️ 未找到应用程序图标")
            return True
        except Exception as e:
            print(f"⚠️ 应用程序图标设置失败: {e}")
            return False
    
    def _log_resource_info(self):
        """记录资源信息"""
        try:
            from utils.resource_manager import log_resource_info
            log_resource_info()
            print("✅ 资源信息记录成功")
            return True
        except Exception as e:
            print(f"⚠️ 资源信息记录失败: {e}")
            return False
    
    def _create_main_window(self):
        """创建主窗口"""
        try:
            from gui.main_window import MainWindow
            self.main_window = MainWindow()
            
            # 应用UI配置
            if self.config:
                ui_config = self.config.get_ui_config()
                self.main_window.setMinimumSize(ui_config['min_width'], ui_config['min_height'])
            
            print("✅ 主窗口创建成功")
            return True
        except Exception as e:
            print(f"❌ 主窗口创建失败: {e}")
            return False
    
    def initialize(self):
        """执行初始化流程"""
        print("🚀 开始应用程序初始化...")
        print("=" * 50)
        
        # 设置默认初始化步骤
        self.setup_default_initialization_steps()
        
        # 执行初始化步骤
        for step in self.initialization_steps:
            print(f"📋 {step['name']}...")
            try:
                success = step['func']()
                if not success and step['critical']:
                    print(f"❌ 关键步骤失败: {step['name']}")
                    return False
            except Exception as e:
                print(f"❌ 步骤执行异常: {step['name']} - {e}")
                if step['critical']:
                    return False
        
        print("=" * 50)
        print("✅ 应用程序初始化完成")
        return True
    
    def run(self):
        """运行应用程序"""
        if not self.initialize():
            print("❌ 应用程序初始化失败")
            return False
        
        try:
            # 显示主窗口
            if self.main_window:
                self.main_window.show()
            
            # 运行应用程序
            if self.app:
                print("🎯 应用程序启动成功，开始运行...")
                sys.exit(self.app.exec())
            else:
                print("❌ 应用程序对象未创建")
                return False
                
        except Exception as e:
            print(f"❌ 应用程序运行失败: {e}")
            traceback.print_exc()
            return False


def create_and_run_app():
    """创建并运行应用程序的便捷函数"""
    initializer = AppInitializer()
    return initializer.run()


if __name__ == "__main__":
    create_and_run_app()
