#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理启动脚本
在程序启动前清理可能的日志文件和文件夹
"""

import os
import sys
import shutil
from pathlib import Path


def clean_log_folders():
    """清理可能生成的日志文件夹"""
    print("🧹 清理日志文件夹...")
    
    # 可能的日志文件夹名称
    log_folders = [
        'log', 'logs', 'Log', 'Logs', 
        '.log', '.logs', 
        'debug', 'Debug',
        'temp', 'tmp',
        '__pycache__'
    ]
    
    cleaned_count = 0
    
    for folder_name in log_folders:
        folder_path = Path(folder_name)
        if folder_path.exists() and folder_path.is_dir():
            try:
                # 检查文件夹内容
                files = list(folder_path.iterdir())
                
                if not files:
                    # 空文件夹，直接删除
                    folder_path.rmdir()
                    print(f"  ✅ 删除空文件夹: {folder_name}")
                    cleaned_count += 1
                else:
                    # 检查是否只包含日志文件
                    log_extensions = ['.log', '.txt', '.out', '.err']
                    all_log_files = all(
                        f.suffix.lower() in log_extensions 
                        for f in files if f.is_file()
                    )
                    
                    if all_log_files:
                        shutil.rmtree(folder_path)
                        print(f"  ✅ 删除日志文件夹: {folder_name} ({len(files)} 个文件)")
                        cleaned_count += 1
                    else:
                        print(f"  ⚠️ 跳过非日志文件夹: {folder_name}")
                        
            except (OSError, PermissionError) as e:
                print(f"  ❌ 无法删除文件夹 {folder_name}: {e}")
    
    if cleaned_count == 0:
        print("  ✅ 没有发现需要清理的日志文件夹")
    else:
        print(f"  🎉 成功清理 {cleaned_count} 个日志文件夹")


def clean_log_files():
    """清理可能生成的日志文件"""
    print("🧹 清理日志文件...")
    
    # 可能的日志文件名称
    log_files = [
        'application.log', 'app.log',
        'debug.log', 'error.log', 'output.log',
        'pyinstaller.log', 'build.log',
        'qt.log', 'gui.log',
        'pandas.log', 'openpyxl.log'
    ]
    
    cleaned_count = 0
    
    for file_name in log_files:
        file_path = Path(file_name)
        if file_path.exists() and file_path.is_file():
            try:
                file_path.unlink()
                print(f"  ✅ 删除日志文件: {file_name}")
                cleaned_count += 1
            except (OSError, PermissionError) as e:
                print(f"  ❌ 无法删除文件 {file_name}: {e}")
    
    if cleaned_count == 0:
        print("  ✅ 没有发现需要清理的日志文件")
    else:
        print(f"  🎉 成功清理 {cleaned_count} 个日志文件")


def clean_temp_files():
    """清理临时文件"""
    print("🧹 清理临时文件...")
    
    # 临时文件模式
    temp_patterns = [
        '*.tmp', '*.temp', 
        '*.pyc', '*.pyo',
        '*.bak', '*.backup',
        '~*', '#*#'
    ]
    
    cleaned_count = 0
    
    for pattern in temp_patterns:
        for temp_file in Path('.').glob(pattern):
            if temp_file.is_file():
                try:
                    temp_file.unlink()
                    print(f"  ✅ 删除临时文件: {temp_file.name}")
                    cleaned_count += 1
                except (OSError, PermissionError) as e:
                    print(f"  ❌ 无法删除文件 {temp_file.name}: {e}")
    
    if cleaned_count == 0:
        print("  ✅ 没有发现需要清理的临时文件")
    else:
        print(f"  🎉 成功清理 {cleaned_count} 个临时文件")


def set_clean_environment():
    """设置干净的环境变量"""
    print("🔧 设置环境变量...")
    
    # 禁用各种日志输出
    env_vars = {
        'PYTHONDONTWRITEBYTECODE': '1',
        'PYTHONIOENCODING': 'utf-8',
        'QT_LOGGING_RULES': '*.debug=false;qt.qpa.*=false',
        'QT_LOGGING_TO_CONSOLE': '0',
        'PYINSTALLER_SUPPRESS_WARNINGS': '1'
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  ✅ 设置环境变量: {key}={value}")


def main():
    """主清理函数"""
    print("🚀 评价任务拆分工具 - 清理启动")
    print("=" * 50)
    
    # 1. 清理日志文件夹
    clean_log_folders()
    
    # 2. 清理日志文件
    clean_log_files()
    
    # 3. 清理临时文件
    clean_temp_files()
    
    # 4. 设置环境变量
    set_clean_environment()
    
    print("=" * 50)
    print("✅ 清理完成，启动主程序...")
    print()
    
    # 启动主程序
    try:
        import main
        main.main()
    except Exception as e:
        print(f"❌ 启动主程序失败: {e}")
        input("按回车键退出...")
        sys.exit(1)


if __name__ == "__main__":
    main()
