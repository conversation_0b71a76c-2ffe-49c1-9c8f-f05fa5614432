#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序配置模块
集中管理应用程序的各种配置参数
"""

from dataclasses import dataclass
from typing import Dict, Any
import os
from pathlib import Path


@dataclass
class AppConfig:
    """应用程序配置类"""
    
    # 应用程序基本信息
    APP_NAME: str = "评价任务拆分工具"
    APP_VERSION: str = "2.1"
    APP_AUTHOR: str = "by：chen"
    APP_STYLE: str = "Fusion"
    
    # 性能配置
    MEMORY_THRESHOLD: float = 0.8
    DEFAULT_CHUNK_SIZE: int = 10000
    CACHE_SIZE: int = 1000
    ENABLE_PERFORMANCE_MONITORING: bool = True
    
    # 文件处理配置
    MAX_FILE_SIZE_MB: int = 100
    SUPPORTED_EXCEL_FORMATS: tuple = ('.xlsx', '.xls')
    DEFAULT_ENCODING: str = 'utf-8'
    
    # UI配置
    WINDOW_MIN_WIDTH: int = 800
    WINDOW_MIN_HEIGHT: int = 600
    PROGRESS_UPDATE_INTERVAL: int = 100  # 毫秒
    
    # 日志配置
    ENABLE_DEBUG_LOGGING: bool = False
    LOG_TO_CONSOLE: bool = True
    LOG_TO_FILE: bool = False
    
    # 环境变量配置
    ENVIRONMENT_VARS: Dict[str, str] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.ENVIRONMENT_VARS is None:
            self.ENVIRONMENT_VARS = {
                'PYTHONDONTWRITEBYTECODE': '1',
                'PYTHONIOENCODING': self.DEFAULT_ENCODING,
                'QT_LOGGING_RULES': '*.debug=false;qt.qpa.*=false;*.info=false',
                'QT_LOGGING_TO_CONSOLE': '0' if not self.LOG_TO_CONSOLE else '1',
                'PYINSTALLER_SUPPRESS_WARNINGS': '1',
                'OPENPYXL_DISABLE_WARNINGS': '1',
                'PANDAS_DISABLE_WARNINGS': '1'
            }
    
    def apply_environment_variables(self):
        """应用环境变量配置"""
        for key, value in self.ENVIRONMENT_VARS.items():
            os.environ[key] = value
    
    def get_file_size_limits(self) -> Dict[str, int]:
        """获取文件大小限制配置"""
        return {
            'max_file_size_mb': self.MAX_FILE_SIZE_MB,
            'chunk_size': self.DEFAULT_CHUNK_SIZE,
            'memory_threshold': self.MEMORY_THRESHOLD
        }
    
    def get_ui_config(self) -> Dict[str, Any]:
        """获取UI配置"""
        return {
            'min_width': self.WINDOW_MIN_WIDTH,
            'min_height': self.WINDOW_MIN_HEIGHT,
            'style': self.APP_STYLE,
            'progress_interval': self.PROGRESS_UPDATE_INTERVAL
        }
    
    def is_development_mode(self) -> bool:
        """检查是否为开发模式"""
        return os.environ.get('APP_ENV', 'production').lower() == 'development'
    
    def get_app_info(self) -> Dict[str, str]:
        """获取应用程序信息"""
        return {
            'name': self.APP_NAME,
            'version': self.APP_VERSION,
            'author': self.APP_AUTHOR
        }


# 全局配置实例
app_config = AppConfig()


def get_config() -> AppConfig:
    """获取全局配置实例"""
    return app_config


def update_config(**kwargs):
    """更新配置参数"""
    global app_config
    for key, value in kwargs.items():
        if hasattr(app_config, key.upper()):
            setattr(app_config, key.upper(), value)


def load_config_from_file(config_file: str = None):
    """从文件加载配置（可选功能）"""
    if config_file is None:
        config_file = Path(__file__).parent / "app_settings.json"
    
    if Path(config_file).exists():
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 更新配置
            for key, value in config_data.items():
                if hasattr(app_config, key.upper()):
                    setattr(app_config, key.upper(), value)
            
            return True
        except Exception as e:
            print(f"⚠️ 加载配置文件失败: {e}")
            return False
    
    return False


def save_config_to_file(config_file: str = None):
    """保存配置到文件（可选功能）"""
    if config_file is None:
        config_file = Path(__file__).parent / "app_settings.json"
    
    try:
        import json
        from dataclasses import asdict
        
        config_data = asdict(app_config)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        return True
    except Exception as e:
        print(f"⚠️ 保存配置文件失败: {e}")
        return False
