#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源管理模块
处理图标、logo等资源文件的加载和管理
确保打包后能够正确访问资源文件
"""

import os
import sys
from pathlib import Path
from PyQt6.QtGui import QIcon, QPixmap
from PyQt6.QtCore import QDir


class ResourceManager:
    """资源管理器"""
    
    def __init__(self):
        self._resource_dir = None
        self._icons = {}  # 图标缓存
        self._init_resource_path()
    
    def _init_resource_path(self):
        """初始化资源路径"""
        if getattr(sys, 'frozen', False):
            # 打包后的环境
            if hasattr(sys, '_MEIPASS'):
                # PyInstaller 打包
                self._resource_dir = Path(sys._MEIPASS)
            else:
                # 其他打包工具
                self._resource_dir = Path(sys.executable).parent
        else:
            # 开发环境
            self._resource_dir = Path(__file__).parent.parent
    
    def get_resource_path(self, relative_path):
        """获取资源文件的绝对路径"""
        resource_path = self._resource_dir / relative_path
        
        # 如果文件不存在，尝试在当前目录查找
        if not resource_path.exists():
            current_dir_path = Path.cwd() / relative_path
            if current_dir_path.exists():
                return str(current_dir_path)
        
        return str(resource_path)
    
    def get_icon(self, icon_name):
        """获取图标，支持缓存"""
        if icon_name in self._icons:
            return self._icons[icon_name]
        
        # 尝试多种图标格式
        icon_extensions = ['.ico', '.png', '.jpg', '.jpeg', '.svg']
        
        for ext in icon_extensions:
            icon_path = self.get_resource_path(f"{icon_name}{ext}")
            if os.path.exists(icon_path):
                icon = QIcon(icon_path)
                if not icon.isNull():
                    self._icons[icon_name] = icon
                    return icon
        
        # 如果找不到图标，返回空图标
        empty_icon = QIcon()
        self._icons[icon_name] = empty_icon
        return empty_icon
    
    def get_pixmap(self, image_name, width=None, height=None):
        """获取图片像素图"""
        image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.gif']
        
        for ext in image_extensions:
            image_path = self.get_resource_path(f"{image_name}{ext}")
            if os.path.exists(image_path):
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    if width and height:
                        pixmap = pixmap.scaled(width, height)
                    return pixmap
        
        # 返回空像素图
        return QPixmap()
    
    def has_resource(self, resource_name):
        """检查资源是否存在"""
        extensions = ['.ico', '.png', '.jpg', '.jpeg', '.svg', '.bmp', '.gif']
        
        for ext in extensions:
            resource_path = self.get_resource_path(f"{resource_name}{ext}")
            if os.path.exists(resource_path):
                return True
        
        return False
    
    def list_available_resources(self):
        """列出可用的资源文件"""
        resources = []
        resource_extensions = ['.ico', '.png', '.jpg', '.jpeg', '.svg', '.bmp', '.gif']
        
        if self._resource_dir.exists():
            for file_path in self._resource_dir.iterdir():
                if file_path.is_file() and file_path.suffix.lower() in resource_extensions:
                    resources.append(file_path.stem)
        
        return resources
    
    def get_app_icon(self):
        """获取应用程序主图标"""
        # 按优先级尝试不同的图标名称
        icon_names = ['favicon', 'logo', 'icon', 'app_icon', 'main_icon']
        
        for icon_name in icon_names:
            icon = self.get_icon(icon_name)
            if not icon.isNull():
                return icon
        
        # 如果都没找到，创建一个默认图标
        return self._create_default_icon()
    
    def _create_default_icon(self):
        """创建默认图标"""
        # 创建一个简单的默认图标
        pixmap = QPixmap(32, 32)
        pixmap.fill()  # 填充为白色
        return QIcon(pixmap)


# 全局资源管理器实例
resource_manager = ResourceManager()


def get_app_icon():
    """获取应用程序图标的便捷函数"""
    return resource_manager.get_app_icon()


def get_resource_path(relative_path):
    """获取资源路径的便捷函数"""
    return resource_manager.get_resource_path(relative_path)


def setup_app_icon(app):
    """为应用程序设置图标"""
    icon = get_app_icon()
    if not icon.isNull():
        app.setWindowIcon(icon)
        return True
    return False


def log_resource_info():
    """记录资源信息（用于调试）"""
    print("🔍 资源管理器信息:")
    print(f"  资源目录: {resource_manager._resource_dir}")
    print(f"  资源目录存在: {resource_manager._resource_dir.exists()}")
    
    available_resources = resource_manager.list_available_resources()
    if available_resources:
        print(f"  可用资源: {', '.join(available_resources)}")
    else:
        print("  未找到资源文件")
    
    # 检查主要图标
    has_favicon = resource_manager.has_resource('favicon')
    print(f"  favicon图标: {'✅ 存在' if has_favicon else '❌ 不存在'}")
    
    if has_favicon:
        favicon_path = resource_manager.get_resource_path('favicon.ico')
        print(f"  favicon路径: {favicon_path}")
