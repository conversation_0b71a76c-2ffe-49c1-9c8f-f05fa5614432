#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本
自动化打包评价任务拆分工具，确保包含所有资源文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def check_requirements():
    """检查打包所需的依赖"""
    print("🔍 检查打包环境...")
    
    # 需要检查的包，格式：(导入名, 显示名)
    required_packages = [
        ('PyInstaller', 'pyinstaller'),
        ('PyQt6', 'PyQt6'),
        ('pandas', 'pandas'),
        ('openpyxl', 'openpyxl')
    ]
    missing_packages = []

    for import_name, display_name in required_packages:
        try:
            __import__(import_name)
            print(f"  ✅ {display_name}")
        except ImportError:
            missing_packages.append(display_name)
            print(f"  ❌ {display_name}")
    
    if missing_packages:
        print(f"\n⚠️ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖检查通过")
    return True


def prepare_resources():
    """准备资源文件"""
    print("\n📁 准备资源文件...")
    
    project_root = Path(__file__).parent
    
    # 检查图标文件
    icon_files = ['favicon.ico', 'logo.png', 'logo.jpg', 'icon.png']
    found_icons = []
    
    for icon_file in icon_files:
        icon_path = project_root / icon_file
        if icon_path.exists():
            found_icons.append(icon_file)
            print(f"  ✅ 找到图标: {icon_file}")
    
    if not found_icons:
        print("  ⚠️ 未找到图标文件，将使用默认图标")
    
    # 检查其他重要文件
    important_files = ['main.py', 'requirements.txt']
    for file_name in important_files:
        file_path = project_root / file_name
        if file_path.exists():
            print(f"  ✅ 找到文件: {file_name}")
        else:
            print(f"  ❌ 缺少文件: {file_name}")
    
    return len(found_icons) > 0


def clean_build_dirs():
    """清理构建目录"""
    print("\n🧹 清理构建目录...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        dir_path = Path(dir_name)
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"  🗑️ 删除目录: {dir_name}")
    
    # 清理.pyc文件
    for pyc_file in Path('.').rglob('*.pyc'):
        pyc_file.unlink()
    
    print("✅ 清理完成")


def build_executable():
    """构建可执行文件"""
    print("\n🔨 开始构建可执行文件...")
    
    # 使用spec文件构建
    spec_file = Path('build_config.spec')
    
    if spec_file.exists():
        print("  📋 使用配置文件: build_config.spec")
        cmd = ['pyinstaller', '--clean', str(spec_file)]
    else:
        print("  📋 使用默认配置")
        # 构建基本的PyInstaller命令
        cmd = [
            'pyinstaller',
            '--onefile',
            '--windowed',
            '--name=评价任务拆分工具',
            '--clean'
        ]
        
        # 添加图标
        icon_path = Path('favicon.ico')
        if icon_path.exists():
            cmd.extend(['--icon', str(icon_path)])
        
        # 添加数据文件
        data_files = ['favicon.ico', 'README.md', 'requirements.txt']
        for data_file in data_files:
            if Path(data_file).exists():
                cmd.extend(['--add-data', f'{data_file};.'])
        
        cmd.append('main.py')
    
    print(f"  🚀 执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 构建成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def verify_build():
    """验证构建结果"""
    print("\n🔍 验证构建结果...")
    
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    exe_files = list(dist_dir.glob('*.exe'))
    if not exe_files:
        print("❌ 未找到可执行文件")
        return False
    
    exe_file = exe_files[0]
    file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
    
    print(f"✅ 找到可执行文件: {exe_file.name}")
    print(f"  📏 文件大小: {file_size:.1f} MB")
    print(f"  📍 文件路径: {exe_file.absolute()}")
    
    return True


def main():
    """主函数"""
    print("🚀 评价任务拆分工具 - 自动打包脚本")
    print("=" * 50)
    
    # 检查环境
    if not check_requirements():
        return False
    
    # 准备资源
    has_icons = prepare_resources()
    
    # 清理构建目录
    clean_build_dirs()
    
    # 构建可执行文件
    if not build_executable():
        return False
    
    # 验证构建结果
    if not verify_build():
        return False
    
    print("\n" + "=" * 50)
    print("🎉 打包完成!")
    print("\n📋 使用说明:")
    print("1. 可执行文件位于 dist/ 目录中")
    print("2. 可以直接运行，无需安装Python环境")
    print("3. 程序会自动加载内置的图标和资源")
    
    if has_icons:
        print("4. ✅ 图标已正确包含在程序中")
    else:
        print("4. ⚠️ 未找到自定义图标，使用默认图标")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
