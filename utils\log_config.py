#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志配置模块
控制程序的日志输出，防止自动生成不必要的log文件夹
"""

import os
import sys
import logging
import threading
import time
from pathlib import Path


def disable_external_logging():
    """禁用外部库的日志输出，防止生成log文件夹"""
    
    # 1. 禁用pandas的日志
    try:
        import pandas as pd
        # 设置pandas不输出警告到文件
        pd.options.mode.chained_assignment = None
    except ImportError:
        pass
    
    # 2. 禁用openpyxl的日志
    try:
        import openpyxl
        # 禁用openpyxl的警告
        import warnings
        warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')
    except ImportError:
        pass
    
    # 3. 禁用matplotlib的日志（如果被间接导入）
    try:
        import matplotlib
        matplotlib.use('Agg')  # 使用非交互式后端
        import matplotlib.pyplot as plt
        plt.ioff()  # 关闭交互模式
    except ImportError:
        pass
    
    # 4. 设置环境变量禁用各种日志
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONDONTWRITEBYTECODE'] = '1'  # 不生成.pyc文件
    
    # 5. 禁用Qt的日志输出
    os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'
    os.environ['QT_LOGGING_TO_CONSOLE'] = '0'
    
    # 6. 禁用PyInstaller运行时的日志
    if hasattr(sys, 'frozen'):
        # 在打包环境中
        os.environ['PYINSTALLER_SUPPRESS_WARNINGS'] = '1'


def configure_application_logging():
    """配置应用程序自身的日志"""
    
    # 获取根日志记录器
    root_logger = logging.getLogger()
    
    # 清除所有现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 设置日志级别为WARNING，只记录重要信息
    root_logger.setLevel(logging.WARNING)
    
    # 如果需要日志，只输出到控制台，不创建文件
    if not hasattr(sys, 'frozen'):  # 只在开发环境中启用控制台日志
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.WARNING)
        formatter = logging.Formatter('%(levelname)s: %(message)s')
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)


def prevent_log_folder_creation():
    """防止创建log文件夹的额外措施"""

    # 1. 检查并删除可能的日志目录
    potential_log_dirs = ['log', 'logs', 'Log', 'Logs', '.log']

    for log_dir in potential_log_dirs:
        log_path = Path(log_dir)
        if log_path.exists() and log_path.is_dir():
            try:
                # 检查是否为空目录
                if not any(log_path.iterdir()):
                    log_path.rmdir()
                    print(f"🗑️ 删除空日志目录: {log_dir}")
            except (OSError, PermissionError):
                pass  # 忽略删除失败的情况

    # 2. 设置工作目录权限（如果可能）
    try:
        current_dir = Path.cwd()
        # 确保当前目录可写，但不鼓励创建日志文件
        pass
    except Exception:
        pass


# 全局变量控制监控线程
_monitor_thread = None
_monitor_running = False


def start_log_folder_monitor():
    """启动日志文件夹监控线程"""
    global _monitor_thread, _monitor_running

    if _monitor_running:
        return

    _monitor_running = True
    _monitor_thread = threading.Thread(target=_log_folder_monitor_worker, daemon=True)
    _monitor_thread.start()
    print("🔍 启动日志文件夹监控线程")


def stop_log_folder_monitor():
    """停止日志文件夹监控线程"""
    global _monitor_running
    _monitor_running = False
    print("⏹️ 停止日志文件夹监控线程")


def _log_folder_monitor_worker():
    """日志文件夹监控工作线程"""
    potential_log_dirs = ['log', 'logs', 'Log', 'Logs', '.log', 'debug', 'Debug']

    while _monitor_running:
        try:
            for log_dir in potential_log_dirs:
                log_path = Path(log_dir)
                if log_path.exists() and log_path.is_dir():
                    try:
                        # 检查是否为空目录
                        files = list(log_path.iterdir())
                        if not files:
                            log_path.rmdir()
                            print(f"🗑️ 实时删除空日志目录: {log_dir}")
                        else:
                            # 检查是否只包含日志文件
                            log_extensions = ['.log', '.txt', '.out', '.err']
                            all_log_files = all(
                                f.suffix.lower() in log_extensions
                                for f in files if f.is_file()
                            )
                            if all_log_files:
                                import shutil
                                shutil.rmtree(log_path)
                                print(f"🗑️ 实时删除日志目录: {log_dir} ({len(files)} 个文件)")
                    except (OSError, PermissionError):
                        pass  # 忽略删除失败的情况

            # 每秒检查一次
            time.sleep(1)

        except Exception:
            # 忽略监控过程中的任何错误
            pass


def setup_clean_environment():
    """设置干净的运行环境，避免生成不必要的文件"""

    print("🧹 配置干净的运行环境...")

    # 1. 禁用外部库日志
    disable_external_logging()

    # 2. 配置应用程序日志
    configure_application_logging()

    # 3. 防止日志文件夹创建
    prevent_log_folder_creation()

    # 4. 启动实时监控
    start_log_folder_monitor()

    # 5. 设置临时目录（如果需要）
    if hasattr(sys, 'frozen'):
        # 在打包环境中，使用系统临时目录
        import tempfile
        temp_dir = tempfile.gettempdir()
        os.environ['TEMP'] = temp_dir
        os.environ['TMP'] = temp_dir

    print("✅ 环境配置完成，已禁用不必要的日志输出")


def cleanup_on_exit():
    """程序退出时的清理工作"""

    # 停止监控线程
    stop_log_folder_monitor()

    # 清理可能生成的临时日志文件
    potential_log_files = [
        'application.log',
        'debug.log',
        'error.log',
        'output.log'
    ]

    for log_file in potential_log_files:
        log_path = Path(log_file)
        if log_path.exists() and log_path.is_file():
            try:
                log_path.unlink()
                print(f"🗑️ 清理日志文件: {log_file}")
            except (OSError, PermissionError):
                pass

    # 最后一次清理空的日志目录
    prevent_log_folder_creation()


# 在模块导入时自动执行基础配置
if __name__ != "__main__":
    # 只在被导入时执行，不在直接运行时执行
    disable_external_logging()


# 注册退出时的清理函数
import atexit
atexit.register(cleanup_on_exit)
