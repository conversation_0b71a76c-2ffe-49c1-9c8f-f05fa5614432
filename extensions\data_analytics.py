#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析与报告模块
提供详细的数据分析、统计报告和可视化功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Tuple, Any
import json


class DataAnalytics:
    """数据分析器"""
    
    def __init__(self):
        self.analysis_results = {}
        self.statistics = {}
        
    def analyze_workload_distribution(self, allocated_data: Dict) -> Dict:
        """分析工作量分布"""
        workload_stats = {}
        
        for staff_name, staff_data in allocated_data.items():
            total_tasks = 0
            operation_counts = {}
            
            for operation_code, data in staff_data.items():
                task_count = len(data['data_rows'])
                total_tasks += task_count
                operation_counts[operation_code] = task_count
            
            workload_stats[staff_name] = {
                'total_tasks': total_tasks,
                'operation_counts': operation_counts,
                'operation_types': len(operation_counts)
            }
        
        return workload_stats
    
    def analyze_operation_complexity(self, operation_data: Dict) -> Dict:
        """分析运营编码复杂度"""
        complexity_analysis = {}
        
        for operation_code, data in operation_data.items():
            rows = data['data_rows']
            
            # 分析数据特征
            complexity_analysis[operation_code] = {
                'total_records': len(rows),
                'avg_text_length': self._calculate_avg_text_length(rows),
                'unique_customers': self._count_unique_customers(rows),
                'complexity_score': self._calculate_complexity_score(rows)
            }
        
        return complexity_analysis
    
    def generate_performance_report(self, allocated_data: Dict, 
                                  processing_time: float) -> Dict:
        """生成性能报告"""
        total_records = sum(
            len(staff_data[op]['data_rows']) 
            for staff_data in allocated_data.values() 
            for op in staff_data
        )
        
        return {
            'processing_time': processing_time,
            'total_records': total_records,
            'records_per_second': total_records / processing_time if processing_time > 0 else 0,
            'staff_count': len(allocated_data),
            'operation_count': sum(len(staff_data) for staff_data in allocated_data.values()),
            'avg_records_per_staff': total_records / len(allocated_data) if allocated_data else 0
        }
    
    def analyze_time_patterns(self, operation_data: Dict) -> Dict:
        """分析时间模式（如果数据中包含时间信息）"""
        time_patterns = {}
        
        for operation_code, data in operation_data.items():
            # 假设数据中有时间列
            time_analysis = {
                'peak_hours': [],
                'daily_distribution': {},
                'weekly_patterns': {}
            }
            time_patterns[operation_code] = time_analysis
        
        return time_patterns
    
    def generate_quality_metrics(self, allocated_data: Dict) -> Dict:
        """生成质量指标"""
        quality_metrics = {
            'balance_score': self._calculate_balance_score(allocated_data),
            'distribution_fairness': self._calculate_distribution_fairness(allocated_data),
            'workload_variance': self._calculate_workload_variance(allocated_data)
        }
        
        return quality_metrics
    
    def _calculate_avg_text_length(self, rows: List) -> float:
        """计算平均文本长度"""
        if not rows:
            return 0
        
        total_length = 0
        count = 0
        
        for row in rows:
            for cell in row:
                if isinstance(cell, str):
                    total_length += len(cell)
                    count += 1
        
        return total_length / count if count > 0 else 0
    
    def _count_unique_customers(self, rows: List) -> int:
        """统计唯一客户数量"""
        # 假设第一列是客户标识
        customers = set()
        for row in rows:
            if row and len(row) > 0:
                customers.add(str(row[0]))
        return len(customers)
    
    def _calculate_complexity_score(self, rows: List) -> float:
        """计算复杂度评分"""
        if not rows:
            return 0
        
        # 基于记录数量、文本长度等计算复杂度
        record_count_score = min(len(rows) / 100, 1.0)  # 标准化到0-1
        text_length_score = min(self._calculate_avg_text_length(rows) / 100, 1.0)
        
        return (record_count_score + text_length_score) / 2
    
    def _calculate_balance_score(self, allocated_data: Dict) -> float:
        """计算分配平衡度评分"""
        if not allocated_data:
            return 0
        
        workloads = []
        for staff_data in allocated_data.values():
            total_tasks = sum(len(data['data_rows']) for data in staff_data.values())
            workloads.append(total_tasks)
        
        if not workloads:
            return 0
        
        mean_workload = np.mean(workloads)
        std_workload = np.std(workloads)
        
        # 标准差越小，平衡度越高
        balance_score = max(0, 1 - (std_workload / mean_workload)) if mean_workload > 0 else 1
        return balance_score
    
    def _calculate_distribution_fairness(self, allocated_data: Dict) -> float:
        """计算分配公平性"""
        # 基于基尼系数计算公平性
        workloads = []
        for staff_data in allocated_data.values():
            total_tasks = sum(len(data['data_rows']) for data in staff_data.values())
            workloads.append(total_tasks)
        
        if not workloads or len(workloads) < 2:
            return 1.0
        
        # 计算基尼系数
        workloads.sort()
        n = len(workloads)
        cumsum = np.cumsum(workloads)
        gini = (n + 1 - 2 * sum((n + 1 - i) * y for i, y in enumerate(workloads))) / (n * sum(workloads))
        
        return 1 - gini  # 转换为公平性指标（越高越公平）
    
    def _calculate_workload_variance(self, allocated_data: Dict) -> float:
        """计算工作量方差"""
        workloads = []
        for staff_data in allocated_data.values():
            total_tasks = sum(len(data['data_rows']) for data in staff_data.values())
            workloads.append(total_tasks)
        
        return np.var(workloads) if workloads else 0
    
    def export_analysis_report(self, output_path: str, 
                             allocated_data: Dict, 
                             operation_data: Dict,
                             processing_time: float):
        """导出完整的分析报告"""
        
        # 生成各种分析
        workload_analysis = self.analyze_workload_distribution(allocated_data)
        complexity_analysis = self.analyze_operation_complexity(operation_data)
        performance_report = self.generate_performance_report(allocated_data, processing_time)
        quality_metrics = self.generate_quality_metrics(allocated_data)
        
        # 创建综合报告
        comprehensive_report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_staff': len(allocated_data),
                'total_operations': len(operation_data),
                'total_records': sum(len(data['data_rows']) for data in operation_data.values()),
                'processing_time': processing_time
            },
            'workload_distribution': workload_analysis,
            'operation_complexity': complexity_analysis,
            'performance_metrics': performance_report,
            'quality_metrics': quality_metrics
        }
        
        # 保存为JSON
        report_path = Path(output_path) / "数据分析报告.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(comprehensive_report, f, ensure_ascii=False, indent=2)
        
        # 生成Excel报告
        self._create_excel_report(comprehensive_report, Path(output_path) / "数据分析报告.xlsx")
        
        return comprehensive_report
    
    def _create_excel_report(self, report_data: Dict, excel_path: Path):
        """创建Excel格式的分析报告"""
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Alignment
            
            wb = openpyxl.Workbook()
            
            # 创建汇总页
            ws_summary = wb.active
            ws_summary.title = "分析汇总"
            
            # 添加标题
            ws_summary['A1'] = "评价任务拆分 - 数据分析报告"
            ws_summary['A1'].font = Font(size=16, bold=True)
            
            # 添加基本信息
            row = 3
            for key, value in report_data['summary'].items():
                ws_summary[f'A{row}'] = key
                ws_summary[f'B{row}'] = value
                row += 1
            
            # 创建工作量分布页
            ws_workload = wb.create_sheet("工作量分布")
            self._add_workload_sheet(ws_workload, report_data['workload_distribution'])
            
            # 创建质量指标页
            ws_quality = wb.create_sheet("质量指标")
            self._add_quality_sheet(ws_quality, report_data['quality_metrics'])
            
            wb.save(excel_path)
            
        except ImportError:
            print("⚠️ openpyxl 不可用，跳过Excel报告生成")
    
    def _add_workload_sheet(self, ws, workload_data: Dict):
        """添加工作量分布工作表"""
        ws['A1'] = "客服姓名"
        ws['B1'] = "总任务数"
        ws['C1'] = "运营编码数量"
        
        row = 2
        for staff_name, data in workload_data.items():
            ws[f'A{row}'] = staff_name
            ws[f'B{row}'] = data['total_tasks']
            ws[f'C{row}'] = data['operation_types']
            row += 1
    
    def _add_quality_sheet(self, ws, quality_data: Dict):
        """添加质量指标工作表"""
        ws['A1'] = "指标名称"
        ws['B1'] = "数值"
        
        row = 2
        for metric_name, value in quality_data.items():
            ws[f'A{row}'] = metric_name
            ws[f'B{row}'] = f"{value:.4f}"
            row += 1


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self):
        self.analytics = DataAnalytics()
    
    def generate_daily_summary(self, allocated_data: Dict, output_path: str):
        """生成每日工作汇总"""
        summary_data = []
        
        for staff_name, staff_data in allocated_data.items():
            for operation_code, data in staff_data.items():
                summary_data.append({
                    '客服姓名': staff_name,
                    '运营编码': operation_code,
                    '任务数量': len(data['data_rows']),
                    '生成时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
        
        # 创建DataFrame并保存
        df = pd.DataFrame(summary_data)
        summary_path = Path(output_path) / "每日工作汇总.xlsx"
        df.to_excel(summary_path, index=False)
        
        return summary_path
    
    def generate_trend_analysis(self, historical_data: List[Dict], output_path: str):
        """生成趋势分析报告"""
        # 这里可以分析历史数据的趋势
        trend_data = {
            'dates': [],
            'total_tasks': [],
            'staff_efficiency': [],
            'processing_time': []
        }
        
        for data in historical_data:
            trend_data['dates'].append(data.get('date', ''))
            trend_data['total_tasks'].append(data.get('total_tasks', 0))
            trend_data['staff_efficiency'].append(data.get('efficiency', 0))
            trend_data['processing_time'].append(data.get('processing_time', 0))
        
        # 保存趋势数据
        df = pd.DataFrame(trend_data)
        trend_path = Path(output_path) / "趋势分析.xlsx"
        df.to_excel(trend_path, index=False)
        
        return trend_path
