#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时监控与预警系统
提供实时状态监控、异常检测和智能预警功能
"""

import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Callable, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import json
from pathlib import Path
import queue


class AlertLevel(Enum):
    """预警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class Alert:
    """预警信息"""
    id: str
    level: AlertLevel
    title: str
    message: str
    timestamp: datetime
    source: str
    data: Dict = field(default_factory=dict)
    resolved: bool = False
    
    def to_dict(self):
        return {
            'id': self.id,
            'level': self.level.value,
            'title': self.title,
            'message': self.message,
            'timestamp': self.timestamp.isoformat(),
            'source': self.source,
            'data': self.data,
            'resolved': self.resolved
        }


@dataclass
class MonitoringMetrics:
    """监控指标"""
    timestamp: datetime
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    processing_speed: float = 0.0  # 记录/秒
    active_tasks: int = 0
    completed_tasks: int = 0
    error_count: int = 0
    staff_workload: Dict[str, int] = field(default_factory=dict)
    
    def to_dict(self):
        return {
            'timestamp': self.timestamp.isoformat(),
            'cpu_usage': self.cpu_usage,
            'memory_usage': self.memory_usage,
            'processing_speed': self.processing_speed,
            'active_tasks': self.active_tasks,
            'completed_tasks': self.completed_tasks,
            'error_count': self.error_count,
            'staff_workload': self.staff_workload
        }


class RealTimeMonitor:
    """实时监控器"""
    
    def __init__(self, update_interval: int = 5):
        self.update_interval = update_interval
        self.is_monitoring = False
        self.metrics_history = []
        self.alerts = []
        self.alert_callbacks = []
        self.monitoring_thread = None
        self.metrics_queue = queue.Queue()
        
        # 监控阈值
        self.thresholds = {
            'cpu_usage': 80.0,      # CPU使用率阈值
            'memory_usage': 85.0,   # 内存使用率阈值
            'error_rate': 0.05,     # 错误率阈值
            'processing_delay': 300, # 处理延迟阈值（秒）
            'workload_imbalance': 0.3  # 工作负载不平衡阈值
        }
        
        # 性能基线
        self.baseline_metrics = {
            'avg_processing_speed': 100,  # 平均处理速度
            'normal_cpu_usage': 30,       # 正常CPU使用率
            'normal_memory_usage': 40     # 正常内存使用率
        }
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        self._create_alert("MONITOR_001", AlertLevel.INFO, 
                          "监控启动", "实时监控系统已启动", "system")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        self._create_alert("MONITOR_002", AlertLevel.INFO, 
                          "监控停止", "实时监控系统已停止", "system")
    
    def add_alert_callback(self, callback: Callable[[Alert], None]):
        """添加预警回调函数"""
        self.alert_callbacks.append(callback)
    
    def update_metrics(self, metrics: MonitoringMetrics):
        """更新监控指标"""
        self.metrics_queue.put(metrics)
    
    def get_current_status(self) -> Dict:
        """获取当前状态"""
        if not self.metrics_history:
            return {
                'status': 'no_data',
                'message': '暂无监控数据',
                'alert_count': 0,
                'uptime': '0分钟'
            }

        latest_metrics = self.metrics_history[-1]
        recent_alerts = [alert for alert in self.alerts[-10:] if not alert.resolved]

        return {
            'status': 'monitoring' if self.is_monitoring else 'stopped',
            'latest_metrics': latest_metrics.to_dict(),
            'recent_alerts': [alert.to_dict() for alert in recent_alerts],
            'alert_count': len(recent_alerts),
            'uptime': self._calculate_uptime()
        }
    
    def get_performance_summary(self, hours: int = 24) -> Dict:
        """获取性能摘要"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_metrics = [m for m in self.metrics_history if m.timestamp > cutoff_time]
        
        if not recent_metrics:
            return {'message': f'过去{hours}小时内无监控数据'}
        
        # 计算统计信息
        cpu_values = [m.cpu_usage for m in recent_metrics]
        memory_values = [m.memory_usage for m in recent_metrics]
        speed_values = [m.processing_speed for m in recent_metrics if m.processing_speed > 0]
        
        return {
            'time_range': f'过去{hours}小时',
            'data_points': len(recent_metrics),
            'cpu_stats': {
                'avg': sum(cpu_values) / len(cpu_values) if cpu_values else 0,
                'max': max(cpu_values) if cpu_values else 0,
                'min': min(cpu_values) if cpu_values else 0
            },
            'memory_stats': {
                'avg': sum(memory_values) / len(memory_values) if memory_values else 0,
                'max': max(memory_values) if memory_values else 0,
                'min': min(memory_values) if memory_values else 0
            },
            'processing_stats': {
                'avg_speed': sum(speed_values) / len(speed_values) if speed_values else 0,
                'total_completed': sum(m.completed_tasks for m in recent_metrics),
                'total_errors': sum(m.error_count for m in recent_metrics)
            }
        }
    
    def _monitoring_loop(self):
        """监控主循环"""
        while self.is_monitoring:
            try:
                # 处理队列中的指标
                while not self.metrics_queue.empty():
                    try:
                        metrics = self.metrics_queue.get_nowait()
                        self._process_metrics(metrics)
                    except queue.Empty:
                        break
                
                # 执行定期检查
                self._perform_health_checks()
                
                # 清理旧数据
                self._cleanup_old_data()
                
                time.sleep(self.update_interval)
                
            except Exception as e:
                self._create_alert("MONITOR_ERROR", AlertLevel.ERROR,
                                 "监控异常", f"监控循环出现异常: {str(e)}", "system")
                time.sleep(self.update_interval)
    
    def _process_metrics(self, metrics: MonitoringMetrics):
        """处理监控指标"""
        # 添加到历史记录
        self.metrics_history.append(metrics)
        
        # 检查阈值
        self._check_thresholds(metrics)
        
        # 检查趋势
        self._check_trends(metrics)
    
    def _check_thresholds(self, metrics: MonitoringMetrics):
        """检查阈值预警"""
        # CPU使用率检查
        if metrics.cpu_usage > self.thresholds['cpu_usage']:
            self._create_alert("CPU_HIGH", AlertLevel.WARNING,
                             "CPU使用率过高", 
                             f"当前CPU使用率: {metrics.cpu_usage:.1f}%",
                             "performance", {'cpu_usage': metrics.cpu_usage})
        
        # 内存使用率检查
        if metrics.memory_usage > self.thresholds['memory_usage']:
            self._create_alert("MEMORY_HIGH", AlertLevel.WARNING,
                             "内存使用率过高",
                             f"当前内存使用率: {metrics.memory_usage:.1f}%",
                             "performance", {'memory_usage': metrics.memory_usage})
        
        # 工作负载不平衡检查
        if metrics.staff_workload:
            workload_imbalance = self._calculate_workload_imbalance(metrics.staff_workload)
            if workload_imbalance > self.thresholds['workload_imbalance']:
                self._create_alert("WORKLOAD_IMBALANCE", AlertLevel.WARNING,
                                 "工作负载不平衡",
                                 f"负载不平衡度: {workload_imbalance:.2f}",
                                 "allocation", {'imbalance': workload_imbalance})
    
    def _check_trends(self, current_metrics: MonitoringMetrics):
        """检查趋势预警"""
        if len(self.metrics_history) < 5:
            return
        
        # 获取最近5个数据点
        recent_metrics = self.metrics_history[-5:]
        
        # 检查处理速度下降趋势
        speeds = [m.processing_speed for m in recent_metrics if m.processing_speed > 0]
        if len(speeds) >= 3:
            # 简单的趋势检测：如果连续下降
            if all(speeds[i] > speeds[i+1] for i in range(len(speeds)-1)):
                self._create_alert("SPEED_DECLINING", AlertLevel.WARNING,
                                 "处理速度下降",
                                 f"处理速度持续下降，当前: {current_metrics.processing_speed:.1f} 记录/秒",
                                 "performance")
        
        # 检查错误率上升趋势
        error_counts = [m.error_count for m in recent_metrics]
        if len(error_counts) >= 3:
            if all(error_counts[i] < error_counts[i+1] for i in range(len(error_counts)-1)):
                self._create_alert("ERROR_INCREASING", AlertLevel.ERROR,
                                 "错误率上升",
                                 f"错误数量持续增加，当前: {current_metrics.error_count}",
                                 "quality")
    
    def _perform_health_checks(self):
        """执行健康检查"""
        # 检查系统资源
        try:
            import psutil
            
            # 获取系统指标
            cpu_percent = psutil.cpu_percent()
            memory_percent = psutil.virtual_memory().percent
            
            # 创建系统指标
            system_metrics = MonitoringMetrics(
                timestamp=datetime.now(),
                cpu_usage=cpu_percent,
                memory_usage=memory_percent
            )
            
            self._process_metrics(system_metrics)
            
        except ImportError:
            pass  # psutil不可用时跳过系统监控
    
    def _calculate_workload_imbalance(self, staff_workload: Dict[str, int]) -> float:
        """计算工作负载不平衡度"""
        if not staff_workload or len(staff_workload) < 2:
            return 0.0
        
        workloads = list(staff_workload.values())
        mean_workload = sum(workloads) / len(workloads)
        
        if mean_workload == 0:
            return 0.0
        
        variance = sum((w - mean_workload) ** 2 for w in workloads) / len(workloads)
        std_dev = variance ** 0.5
        
        return std_dev / mean_workload
    
    def _create_alert(self, alert_id: str, level: AlertLevel, title: str, 
                     message: str, source: str, data: Dict = None):
        """创建预警"""
        alert = Alert(
            id=f"{alert_id}_{int(time.time())}",
            level=level,
            title=title,
            message=message,
            timestamp=datetime.now(),
            source=source,
            data=data or {}
        )
        
        self.alerts.append(alert)
        
        # 触发回调
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                print(f"预警回调执行失败: {e}")
    
    def _calculate_uptime(self) -> str:
        """计算运行时间"""
        if not self.metrics_history:
            return "0分钟"
        
        start_time = self.metrics_history[0].timestamp
        uptime = datetime.now() - start_time
        
        hours = int(uptime.total_seconds() // 3600)
        minutes = int((uptime.total_seconds() % 3600) // 60)
        
        if hours > 0:
            return f"{hours}小时{minutes}分钟"
        else:
            return f"{minutes}分钟"
    
    def _cleanup_old_data(self):
        """清理旧数据"""
        # 保留最近24小时的数据
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        # 清理指标历史
        self.metrics_history = [m for m in self.metrics_history if m.timestamp > cutoff_time]
        
        # 清理已解决的旧预警
        cutoff_time_alerts = datetime.now() - timedelta(hours=72)  # 保留3天的预警
        self.alerts = [a for a in self.alerts 
                      if a.timestamp > cutoff_time_alerts or not a.resolved]
    
    def resolve_alert(self, alert_id: str):
        """解决预警"""
        for alert in self.alerts:
            if alert.id == alert_id:
                alert.resolved = True
                break
    
    def export_monitoring_data(self, file_path: str, hours: int = 24):
        """导出监控数据"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        export_data = {
            'export_time': datetime.now().isoformat(),
            'time_range_hours': hours,
            'metrics': [m.to_dict() for m in self.metrics_history if m.timestamp > cutoff_time],
            'alerts': [a.to_dict() for a in self.alerts if a.timestamp > cutoff_time],
            'thresholds': self.thresholds,
            'baseline_metrics': self.baseline_metrics
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)


class AlertManager:
    """预警管理器"""
    
    def __init__(self):
        self.notification_channels = []
        self.alert_rules = []
        self.suppression_rules = []
    
    def add_notification_channel(self, channel: Callable[[Alert], None]):
        """添加通知渠道"""
        self.notification_channels.append(channel)
    
    def process_alert(self, alert: Alert):
        """处理预警"""
        # 检查抑制规则
        if self._is_suppressed(alert):
            return
        
        # 发送通知
        for channel in self.notification_channels:
            try:
                channel(alert)
            except Exception as e:
                print(f"发送预警通知失败: {e}")
    
    def _is_suppressed(self, alert: Alert) -> bool:
        """检查预警是否被抑制"""
        # 简单的抑制逻辑：相同类型的预警在5分钟内只发送一次
        recent_time = datetime.now() - timedelta(minutes=5)
        
        for existing_alert in self.suppression_rules:
            if (existing_alert['source'] == alert.source and 
                existing_alert['title'] == alert.title and
                existing_alert['timestamp'] > recent_time):
                return True
        
        # 记录当前预警用于抑制
        self.suppression_rules.append({
            'source': alert.source,
            'title': alert.title,
            'timestamp': datetime.now()
        })
        
        return False


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self, monitor: RealTimeMonitor):
        self.monitor = monitor
    
    def analyze_performance_degradation(self) -> Dict:
        """分析性能下降"""
        if len(self.monitor.metrics_history) < 10:
            return {'status': 'insufficient_data'}
        
        recent_metrics = self.monitor.metrics_history[-10:]
        baseline = self.monitor.baseline_metrics
        
        analysis = {
            'cpu_trend': self._analyze_trend([m.cpu_usage for m in recent_metrics]),
            'memory_trend': self._analyze_trend([m.memory_usage for m in recent_metrics]),
            'speed_degradation': self._analyze_speed_degradation(recent_metrics, baseline),
            'recommendations': []
        }
        
        # 生成建议
        if analysis['cpu_trend'] == 'increasing':
            analysis['recommendations'].append("CPU使用率持续上升，建议检查是否有性能瓶颈")
        
        if analysis['memory_trend'] == 'increasing':
            analysis['recommendations'].append("内存使用率持续上升，建议检查内存泄漏")
        
        return analysis
    
    def _analyze_trend(self, values: List[float]) -> str:
        """分析趋势"""
        if len(values) < 3:
            return 'unknown'
        
        # 简单的趋势分析
        increases = sum(1 for i in range(1, len(values)) if values[i] > values[i-1])
        decreases = sum(1 for i in range(1, len(values)) if values[i] < values[i-1])
        
        if increases > decreases * 1.5:
            return 'increasing'
        elif decreases > increases * 1.5:
            return 'decreasing'
        else:
            return 'stable'
    
    def _analyze_speed_degradation(self, metrics: List[MonitoringMetrics], baseline: Dict) -> Dict:
        """分析速度下降"""
        speeds = [m.processing_speed for m in metrics if m.processing_speed > 0]
        
        if not speeds:
            return {'status': 'no_data'}
        
        avg_speed = sum(speeds) / len(speeds)
        baseline_speed = baseline.get('avg_processing_speed', 100)
        
        degradation_percent = (baseline_speed - avg_speed) / baseline_speed * 100
        
        return {
            'current_avg_speed': avg_speed,
            'baseline_speed': baseline_speed,
            'degradation_percent': degradation_percent,
            'status': 'degraded' if degradation_percent > 20 else 'normal'
        }
