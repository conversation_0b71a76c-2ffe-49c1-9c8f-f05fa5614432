#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能调度与优化算法模块
提供基于机器学习的智能分配算法和动态优化功能
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
from pathlib import Path


@dataclass
class StaffProfile:
    """客服档案"""
    name: str
    skill_level: float = 1.0  # 技能水平 (0.5-2.0)
    efficiency_score: float = 1.0  # 效率评分
    specialties: List[str] = None  # 专长领域
    max_workload: int = 100  # 最大工作量
    preferred_operations: List[str] = None  # 偏好的运营编码
    historical_performance: Dict = None  # 历史表现数据
    
    def __post_init__(self):
        if self.specialties is None:
            self.specialties = []
        if self.preferred_operations is None:
            self.preferred_operations = []
        if self.historical_performance is None:
            self.historical_performance = {}


@dataclass
class TaskProfile:
    """任务档案"""
    operation_code: str
    complexity_score: float = 1.0  # 复杂度评分
    estimated_time: float = 1.0  # 预估处理时间
    priority_level: int = 1  # 优先级 (1-5)
    required_skills: List[str] = None  # 所需技能
    customer_type: str = "normal"  # 客户类型
    
    def __post_init__(self):
        if self.required_skills is None:
            self.required_skills = []


class SmartScheduler:
    """智能调度器"""
    
    def __init__(self):
        self.staff_profiles = {}
        self.task_profiles = {}
        self.learning_data = []
        self.optimization_weights = {
            'balance': 0.4,      # 负载平衡权重
            'skill_match': 0.3,  # 技能匹配权重
            'efficiency': 0.2,   # 效率权重
            'priority': 0.1      # 优先级权重
        }
    
    def create_staff_profile(self, staff_name: str, **kwargs) -> StaffProfile:
        """创建客服档案"""
        profile = StaffProfile(name=staff_name, **kwargs)
        self.staff_profiles[staff_name] = profile
        return profile
    
    def create_task_profile(self, operation_code: str, **kwargs) -> TaskProfile:
        """创建任务档案"""
        profile = TaskProfile(operation_code=operation_code, **kwargs)
        self.task_profiles[operation_code] = profile
        return profile
    
    def analyze_historical_data(self, historical_allocations: List[Dict]):
        """分析历史分配数据，学习最优模式"""
        for allocation in historical_allocations:
            # 提取特征
            features = self._extract_features(allocation)
            # 计算结果质量
            quality_score = self._calculate_quality_score(allocation)
            
            self.learning_data.append({
                'features': features,
                'quality': quality_score,
                'timestamp': allocation.get('timestamp', datetime.now())
            })
    
    def intelligent_allocation(self, operation_data: Dict, available_staff: List[str]) -> Dict:
        """智能分配算法"""
        # 1. 分析任务特征
        task_analysis = self._analyze_tasks(operation_data)
        
        # 2. 评估客服能力
        staff_analysis = self._analyze_staff_capacity(available_staff)
        
        # 3. 计算最优匹配
        allocation_matrix = self._calculate_allocation_matrix(task_analysis, staff_analysis)
        
        # 4. 执行分配
        allocation_result = self._execute_allocation(allocation_matrix, operation_data)
        
        # 5. 优化调整
        optimized_result = self._optimize_allocation(allocation_result)
        
        return optimized_result
    
    def dynamic_rebalancing(self, current_allocation: Dict, 
                          real_time_feedback: Dict) -> Dict:
        """动态重新平衡"""
        # 分析当前负载
        load_analysis = self._analyze_current_load(current_allocation)
        
        # 识别瓶颈
        bottlenecks = self._identify_bottlenecks(load_analysis, real_time_feedback)
        
        # 生成重平衡方案
        rebalance_plan = self._generate_rebalance_plan(bottlenecks, current_allocation)
        
        return rebalance_plan
    
    def predict_completion_time(self, allocation: Dict) -> Dict:
        """预测完成时间"""
        predictions = {}
        
        for staff_name, tasks in allocation.items():
            staff_profile = self.staff_profiles.get(staff_name)
            if not staff_profile:
                continue
            
            total_estimated_time = 0
            for operation_code, data in tasks.items():
                task_profile = self.task_profiles.get(operation_code)
                if task_profile:
                    task_count = len(data['data_rows'])
                    base_time = task_profile.estimated_time * task_count
                    # 根据客服效率调整
                    adjusted_time = base_time / staff_profile.efficiency_score
                    total_estimated_time += adjusted_time
            
            predictions[staff_name] = {
                'estimated_hours': total_estimated_time,
                'completion_time': datetime.now() + timedelta(hours=total_estimated_time),
                'confidence': self._calculate_prediction_confidence(staff_name)
            }
        
        return predictions
    
    def _analyze_tasks(self, operation_data: Dict) -> Dict:
        """分析任务特征"""
        task_analysis = {}
        
        for operation_code, data in operation_data.items():
            # 获取或创建任务档案
            if operation_code not in self.task_profiles:
                self.task_profiles[operation_code] = self._auto_create_task_profile(operation_code, data)
            
            task_profile = self.task_profiles[operation_code]
            task_count = len(data['data_rows'])
            
            task_analysis[operation_code] = {
                'profile': task_profile,
                'count': task_count,
                'total_complexity': task_profile.complexity_score * task_count,
                'estimated_total_time': task_profile.estimated_time * task_count
            }
        
        return task_analysis
    
    def _analyze_staff_capacity(self, available_staff: List[str]) -> Dict:
        """分析客服能力"""
        staff_analysis = {}
        
        for staff_name in available_staff:
            # 获取或创建客服档案
            if staff_name not in self.staff_profiles:
                self.staff_profiles[staff_name] = StaffProfile(name=staff_name)
            
            profile = self.staff_profiles[staff_name]
            
            staff_analysis[staff_name] = {
                'profile': profile,
                'available_capacity': profile.max_workload,
                'efficiency_factor': profile.efficiency_score,
                'skill_score': profile.skill_level
            }
        
        return staff_analysis
    
    def _calculate_allocation_matrix(self, task_analysis: Dict, staff_analysis: Dict) -> np.ndarray:
        """计算分配矩阵"""
        tasks = list(task_analysis.keys())
        staff = list(staff_analysis.keys())
        
        matrix = np.zeros((len(tasks), len(staff)))
        
        for i, task_code in enumerate(tasks):
            task_info = task_analysis[task_code]
            
            for j, staff_name in enumerate(staff):
                staff_info = staff_analysis[staff_name]
                
                # 计算匹配分数
                score = self._calculate_match_score(task_info, staff_info)
                matrix[i][j] = score
        
        return matrix
    
    def _calculate_match_score(self, task_info: Dict, staff_info: Dict) -> float:
        """计算任务-客服匹配分数"""
        task_profile = task_info['profile']
        staff_profile = staff_info['profile']
        
        # 技能匹配分数
        skill_score = self._calculate_skill_match(task_profile, staff_profile)
        
        # 效率分数
        efficiency_score = staff_profile.efficiency_score
        
        # 负载平衡分数（容量越大分数越高）
        capacity_score = staff_info['available_capacity'] / 100
        
        # 优先级分数
        priority_score = task_profile.priority_level / 5
        
        # 加权计算总分
        total_score = (
            skill_score * self.optimization_weights['skill_match'] +
            efficiency_score * self.optimization_weights['efficiency'] +
            capacity_score * self.optimization_weights['balance'] +
            priority_score * self.optimization_weights['priority']
        )
        
        return total_score
    
    def _calculate_skill_match(self, task_profile: TaskProfile, staff_profile: StaffProfile) -> float:
        """计算技能匹配度"""
        if not task_profile.required_skills:
            return 1.0  # 无特殊技能要求
        
        matched_skills = set(task_profile.required_skills) & set(staff_profile.specialties)
        match_ratio = len(matched_skills) / len(task_profile.required_skills)
        
        return match_ratio
    
    def _execute_allocation(self, allocation_matrix: np.ndarray, operation_data: Dict) -> Dict:
        """执行分配算法"""
        # 使用匈牙利算法或贪心算法进行分配
        tasks = list(operation_data.keys())
        staff = list(self.staff_profiles.keys())
        
        allocation_result = {staff_name: {} for staff_name in staff}
        
        # 简化的贪心分配算法
        for i, task_code in enumerate(tasks):
            # 找到最佳匹配的客服
            best_staff_idx = np.argmax(allocation_matrix[i])
            best_staff = staff[best_staff_idx]
            
            # 分配任务
            allocation_result[best_staff][task_code] = operation_data[task_code]
        
        return allocation_result
    
    def _optimize_allocation(self, allocation_result: Dict) -> Dict:
        """优化分配结果"""
        # 检查负载平衡
        workloads = {}
        for staff_name, tasks in allocation_result.items():
            total_load = sum(len(data['data_rows']) for data in tasks.values())
            workloads[staff_name] = total_load
        
        # 如果负载不平衡，进行调整
        if self._is_unbalanced(workloads):
            allocation_result = self._rebalance_workload(allocation_result, workloads)
        
        return allocation_result
    
    def _is_unbalanced(self, workloads: Dict) -> bool:
        """检查负载是否不平衡"""
        if not workloads:
            return False
        
        loads = list(workloads.values())
        mean_load = np.mean(loads)
        std_load = np.std(loads)
        
        # 如果标准差超过平均值的30%，认为不平衡
        return std_load > mean_load * 0.3
    
    def _rebalance_workload(self, allocation: Dict, workloads: Dict) -> Dict:
        """重新平衡工作负载"""
        # 找到负载最高和最低的客服
        max_staff = max(workloads, key=workloads.get)
        min_staff = min(workloads, key=workloads.get)
        
        # 如果差异显著，转移部分任务
        if workloads[max_staff] - workloads[min_staff] > 10:
            # 选择一个较小的任务进行转移
            tasks_to_move = []
            for task_code, data in allocation[max_staff].items():
                if len(data['data_rows']) <= 5:  # 小任务
                    tasks_to_move.append(task_code)
                    break
            
            # 执行转移
            for task_code in tasks_to_move:
                allocation[min_staff][task_code] = allocation[max_staff].pop(task_code)
        
        return allocation
    
    def _auto_create_task_profile(self, operation_code: str, data: Dict) -> TaskProfile:
        """自动创建任务档案"""
        # 基于数据特征自动估算复杂度
        record_count = len(data['data_rows'])
        
        # 简单的复杂度估算
        if record_count > 50:
            complexity = 2.0
        elif record_count > 20:
            complexity = 1.5
        else:
            complexity = 1.0
        
        return TaskProfile(
            operation_code=operation_code,
            complexity_score=complexity,
            estimated_time=complexity * 0.5,  # 假设每个复杂度单位需要0.5小时
            priority_level=1
        )
    
    def _extract_features(self, allocation: Dict) -> Dict:
        """从分配结果中提取特征"""
        # 提取用于机器学习的特征
        features = {
            'staff_count': len(allocation),
            'total_tasks': sum(len(tasks) for tasks in allocation.values()),
            'load_variance': np.var([len(tasks) for tasks in allocation.values()]),
            'avg_complexity': 1.0  # 简化
        }
        return features
    
    def _calculate_quality_score(self, allocation: Dict) -> float:
        """计算分配质量分数"""
        # 基于平衡度、效率等计算质量分数
        workloads = [len(tasks) for tasks in allocation.values()]
        balance_score = 1 - (np.std(workloads) / np.mean(workloads)) if workloads else 0
        return max(0, balance_score)
    
    def _analyze_current_load(self, allocation: Dict) -> Dict:
        """分析当前负载"""
        load_analysis = {}
        for staff_name, tasks in allocation.items():
            total_tasks = sum(len(data['data_rows']) for data in tasks.values())
            load_analysis[staff_name] = {
                'current_load': total_tasks,
                'capacity_utilization': total_tasks / 100,  # 假设最大容量100
                'task_types': len(tasks)
            }
        return load_analysis
    
    def _identify_bottlenecks(self, load_analysis: Dict, feedback: Dict) -> List[str]:
        """识别瓶颈"""
        bottlenecks = []
        for staff_name, analysis in load_analysis.items():
            if analysis['capacity_utilization'] > 0.9:  # 超过90%容量
                bottlenecks.append(staff_name)
        return bottlenecks
    
    def _generate_rebalance_plan(self, bottlenecks: List[str], allocation: Dict) -> Dict:
        """生成重平衡计划"""
        plan = {
            'bottlenecks': bottlenecks,
            'recommended_actions': [],
            'estimated_improvement': 0
        }
        
        for bottleneck_staff in bottlenecks:
            plan['recommended_actions'].append({
                'action': 'redistribute_tasks',
                'from_staff': bottleneck_staff,
                'suggested_tasks': list(allocation[bottleneck_staff].keys())[:2]  # 建议转移前2个任务
            })
        
        return plan
    
    def _calculate_prediction_confidence(self, staff_name: str) -> float:
        """计算预测置信度"""
        # 基于历史数据的准确性计算置信度
        return 0.85  # 简化返回固定值
    
    def save_profiles(self, file_path: str):
        """保存档案数据"""
        data = {
            'staff_profiles': {name: profile.__dict__ for name, profile in self.staff_profiles.items()},
            'task_profiles': {code: profile.__dict__ for code, profile in self.task_profiles.items()},
            'optimization_weights': self.optimization_weights
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)
    
    def load_profiles(self, file_path: str):
        """加载档案数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 重建对象
            for name, profile_data in data.get('staff_profiles', {}).items():
                self.staff_profiles[name] = StaffProfile(**profile_data)
            
            for code, profile_data in data.get('task_profiles', {}).items():
                self.task_profiles[code] = TaskProfile(**profile_data)
            
            self.optimization_weights.update(data.get('optimization_weights', {}))
            
        except FileNotFoundError:
            print(f"档案文件不存在: {file_path}")
        except Exception as e:
            print(f"加载档案失败: {e}")


class AdaptiveLearning:
    """自适应学习模块"""
    
    def __init__(self, scheduler: SmartScheduler):
        self.scheduler = scheduler
        self.feedback_history = []
    
    def collect_feedback(self, allocation_id: str, staff_feedback: Dict, performance_metrics: Dict):
        """收集反馈数据"""
        feedback_entry = {
            'allocation_id': allocation_id,
            'timestamp': datetime.now(),
            'staff_feedback': staff_feedback,
            'performance_metrics': performance_metrics
        }
        self.feedback_history.append(feedback_entry)
    
    def update_profiles(self):
        """基于反馈更新档案"""
        # 分析反馈数据，更新客服和任务档案
        for feedback in self.feedback_history[-10:]:  # 使用最近10次反馈
            self._update_staff_profiles(feedback)
            self._update_task_profiles(feedback)
    
    def _update_staff_profiles(self, feedback: Dict):
        """更新客服档案"""
        for staff_name, metrics in feedback['performance_metrics'].items():
            if staff_name in self.scheduler.staff_profiles:
                profile = self.scheduler.staff_profiles[staff_name]
                # 根据表现调整效率分数
                if metrics.get('completion_rate', 0) > 0.9:
                    profile.efficiency_score = min(2.0, profile.efficiency_score * 1.05)
                elif metrics.get('completion_rate', 0) < 0.7:
                    profile.efficiency_score = max(0.5, profile.efficiency_score * 0.95)
    
    def _update_task_profiles(self, feedback: Dict):
        """更新任务档案"""
        # 根据实际处理时间调整任务复杂度
        for task_code, actual_time in feedback.get('task_times', {}).items():
            if task_code in self.scheduler.task_profiles:
                profile = self.scheduler.task_profiles[task_code]
                # 调整预估时间
                profile.estimated_time = (profile.estimated_time + actual_time) / 2
