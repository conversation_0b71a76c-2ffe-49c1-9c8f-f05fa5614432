#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目清理脚本
清理测试文件、缓存文件和临时文件
"""

import os
import shutil
from pathlib import Path
import glob


def clean_pycache():
    """清理Python缓存文件"""
    print("🧹 清理Python缓存文件...")
    
    # 查找所有__pycache__目录
    pycache_dirs = []
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            pycache_dirs.append(os.path.join(root, '__pycache__'))
    
    removed_count = 0
    for pycache_dir in pycache_dirs:
        try:
            shutil.rmtree(pycache_dir)
            print(f"  ✅ 删除: {pycache_dir}")
            removed_count += 1
        except Exception as e:
            print(f"  ❌ 删除失败: {pycache_dir} - {e}")
    
    print(f"  📊 共删除 {removed_count} 个缓存目录")


def clean_test_files():
    """清理测试文件"""
    print("\n🧪 清理测试文件...")
    
    test_patterns = [
        'test_*.py',
        'demo_*.py',
        '*_test.py',
        'test_*.txt',
        'test_*.csv',
        'test_*.xlsx',
        'test_*.json',
        'temp_*.py'
    ]
    
    removed_count = 0
    for pattern in test_patterns:
        files = glob.glob(pattern)
        for file in files:
            try:
                os.remove(file)
                print(f"  ✅ 删除: {file}")
                removed_count += 1
            except Exception as e:
                print(f"  ❌ 删除失败: {file} - {e}")
    
    print(f"  📊 共删除 {removed_count} 个测试文件")


def clean_temp_files():
    """清理临时文件"""
    print("\n🗂️ 清理临时文件...")
    
    temp_patterns = [
        '*.tmp',
        '*.temp',
        '*.bak',
        '*.log',
        '.DS_Store',
        'Thumbs.db',
        '*.pyc',
        '*.pyo'
    ]
    
    removed_count = 0
    for pattern in temp_patterns:
        files = glob.glob(pattern, recursive=True)
        for file in files:
            try:
                os.remove(file)
                print(f"  ✅ 删除: {file}")
                removed_count += 1
            except Exception as e:
                print(f"  ❌ 删除失败: {file} - {e}")
    
    print(f"  📊 共删除 {removed_count} 个临时文件")


def clean_build_files():
    """清理构建文件"""
    print("\n🔨 清理构建文件...")
    
    build_dirs = ['build', 'dist']
    removed_count = 0
    
    for build_dir in build_dirs:
        if os.path.exists(build_dir):
            try:
                # 只清理内容，保留目录结构
                for item in os.listdir(build_dir):
                    item_path = os.path.join(build_dir, item)
                    if os.path.isfile(item_path):
                        os.remove(item_path)
                        print(f"  ✅ 删除文件: {item_path}")
                        removed_count += 1
                    elif os.path.isdir(item_path):
                        shutil.rmtree(item_path)
                        print(f"  ✅ 删除目录: {item_path}")
                        removed_count += 1
            except Exception as e:
                print(f"  ❌ 清理失败: {build_dir} - {e}")
    
    print(f"  📊 共清理 {removed_count} 个构建文件/目录")


def clean_logs():
    """清理日志文件"""
    print("\n📋 清理日志文件...")
    
    log_patterns = [
        '*.log',
        'logs/*.log',
        'log/*.log'
    ]
    
    removed_count = 0
    for pattern in log_patterns:
        files = glob.glob(pattern, recursive=True)
        for file in files:
            try:
                os.remove(file)
                print(f"  ✅ 删除: {file}")
                removed_count += 1
            except Exception as e:
                print(f"  ❌ 删除失败: {file} - {e}")
    
    # 删除空的日志目录
    log_dirs = ['logs', 'log']
    for log_dir in log_dirs:
        if os.path.exists(log_dir) and not os.listdir(log_dir):
            try:
                os.rmdir(log_dir)
                print(f"  ✅ 删除空目录: {log_dir}")
                removed_count += 1
            except Exception as e:
                print(f"  ❌ 删除目录失败: {log_dir} - {e}")
    
    print(f"  📊 共删除 {removed_count} 个日志文件/目录")


def show_cleanup_summary():
    """显示清理前的文件统计"""
    print("📊 清理前文件统计:")
    
    # 统计各种文件
    stats = {
        'Python缓存': len(list(Path('.').rglob('__pycache__'))),
        '测试文件': len(glob.glob('test_*.py') + glob.glob('demo_*.py')),
        '临时文件': len(glob.glob('*.tmp') + glob.glob('*.temp') + glob.glob('*.bak')),
        '日志文件': len(glob.glob('*.log', recursive=True))
    }
    
    for file_type, count in stats.items():
        print(f"  {file_type}: {count} 个")


def main():
    """主清理函数"""
    print("🚀 项目清理工具")
    print("=" * 50)
    
    # 显示清理前统计
    show_cleanup_summary()
    print()
    
    # 询问用户要清理什么
    print("请选择要清理的内容:")
    print("1. 全部清理")
    print("2. 仅清理缓存文件")
    print("3. 仅清理测试文件")
    print("4. 仅清理临时文件")
    print("5. 仅清理构建文件")
    print("6. 仅清理日志文件")
    print("0. 取消")
    
    try:
        choice = input("\n请输入选择 (0-6): ").strip()
        
        if choice == '0':
            print("❌ 取消清理")
            return
        elif choice == '1':
            clean_pycache()
            clean_test_files()
            clean_temp_files()
            clean_build_files()
            clean_logs()
        elif choice == '2':
            clean_pycache()
        elif choice == '3':
            clean_test_files()
        elif choice == '4':
            clean_temp_files()
        elif choice == '5':
            clean_build_files()
        elif choice == '6':
            clean_logs()
        else:
            print("❌ 无效选择")
            return
        
        print("\n" + "=" * 50)
        print("✅ 清理完成!")
        
    except KeyboardInterrupt:
        print("\n❌ 用户取消清理")
    except Exception as e:
        print(f"\n❌ 清理过程中出现错误: {e}")


if __name__ == "__main__":
    main()
