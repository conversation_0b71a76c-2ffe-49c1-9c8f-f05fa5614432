#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多格式数据支持模块
支持多种数据格式的导入导出，包括CSV、JSON、XML、数据库等
"""

import pandas as pd
import json
import csv
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime
import sqlite3
import zipfile
import tempfile


@dataclass
class DataFormat:
    """数据格式定义"""
    name: str
    extension: str
    description: str
    supports_import: bool = True
    supports_export: bool = True
    requires_schema: bool = False


class FormatRegistry:
    """格式注册表"""
    
    def __init__(self):
        self.formats = {
            'excel': DataFormat('Excel', '.xlsx', 'Microsoft Excel文件'),
            'csv': DataFormat('CSV', '.csv', '逗号分隔值文件'),
            'json': DataFormat('JSON', '.json', 'JSON数据文件'),
            'xml': DataFormat('XML', '.xml', 'XML数据文件'),
            'sqlite': DataFormat('SQLite', '.db', 'SQLite数据库文件'),
            'txt': DataFormat('Text', '.txt', '纯文本文件'),
            'tsv': DataFormat('TSV', '.tsv', '制表符分隔值文件'),
            'parquet': DataFormat('Parquet', '.parquet', 'Apache Parquet文件', requires_schema=True)
        }
    
    def get_supported_formats(self, operation: str = 'both') -> Dict[str, DataFormat]:
        """获取支持的格式"""
        if operation == 'import':
            return {k: v for k, v in self.formats.items() if v.supports_import}
        elif operation == 'export':
            return {k: v for k, v in self.formats.items() if v.supports_export}
        else:
            return self.formats.copy()
    
    def get_format_by_extension(self, file_path: str) -> Optional[DataFormat]:
        """根据文件扩展名获取格式"""
        ext = Path(file_path).suffix.lower()
        for format_info in self.formats.values():
            if format_info.extension == ext:
                return format_info
        return None


class UniversalDataImporter:
    """通用数据导入器"""
    
    def __init__(self):
        self.registry = FormatRegistry()
        self.import_handlers = {
            'excel': self._import_excel,
            'csv': self._import_csv,
            'json': self._import_json,
            'xml': self._import_xml,
            'sqlite': self._import_sqlite,
            'txt': self._import_text,
            'tsv': self._import_tsv
        }
    
    def import_data(self, file_path: str, format_hint: str = None, **kwargs) -> Dict[str, Any]:
        """导入数据"""
        # 自动检测格式
        if format_hint:
            format_info = self.registry.formats.get(format_hint)
        else:
            format_info = self.registry.get_format_by_extension(file_path)
        
        if not format_info:
            raise ValueError(f"不支持的文件格式: {file_path}")
        
        # 获取处理器
        handler = self.import_handlers.get(format_hint or self._detect_format(file_path))
        if not handler:
            raise ValueError(f"没有找到格式处理器: {format_info.name}")
        
        try:
            return handler(file_path, **kwargs)
        except Exception as e:
            raise Exception(f"导入文件失败 ({format_info.name}): {str(e)}")
    
    def _detect_format(self, file_path: str) -> str:
        """检测文件格式"""
        ext = Path(file_path).suffix.lower()
        format_map = {
            '.xlsx': 'excel',
            '.xls': 'excel',
            '.csv': 'csv',
            '.json': 'json',
            '.xml': 'xml',
            '.db': 'sqlite',
            '.sqlite': 'sqlite',
            '.txt': 'txt',
            '.tsv': 'tsv'
        }
        return format_map.get(ext, 'txt')
    
    def _import_excel(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """导入Excel文件"""
        sheet_name = kwargs.get('sheet_name', None)

        if sheet_name:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            return {'data': df, 'format': 'excel', 'sheets': [sheet_name]}
        else:
            # 读取所有工作表
            excel_file = pd.ExcelFile(file_path)
            sheets_data = {}
            for sheet in excel_file.sheet_names:
                sheets_data[sheet] = pd.read_excel(file_path, sheet_name=sheet)

            # 如果只有一个工作表，直接返回DataFrame
            if len(excel_file.sheet_names) == 1:
                return {
                    'data': list(sheets_data.values())[0],
                    'format': 'excel',
                    'sheets': excel_file.sheet_names
                }
            else:
                return {
                    'data': sheets_data,
                    'format': 'excel',
                    'sheets': excel_file.sheet_names
                }
    
    def _import_csv(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """导入CSV文件"""
        encoding = kwargs.get('encoding', 'utf-8')
        delimiter = kwargs.get('delimiter', ',')
        
        df = pd.read_csv(file_path, encoding=encoding, delimiter=delimiter)
        return {
            'data': df,
            'format': 'csv',
            'encoding': encoding,
            'delimiter': delimiter
        }
    
    def _import_json(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """导入JSON文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 尝试转换为DataFrame
        try:
            if isinstance(data, list):
                df = pd.DataFrame(data)
            elif isinstance(data, dict):
                # 如果是嵌套字典，尝试展平
                df = pd.json_normalize(data)
            else:
                df = pd.DataFrame([data])
            
            return {'data': df, 'format': 'json', 'raw_data': data}
        except:
            return {'data': data, 'format': 'json', 'raw_data': data}
    
    def _import_xml(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """导入XML文件"""
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        # 简单的XML到字典转换
        def xml_to_dict(element):
            result = {}
            for child in element:
                if len(child) == 0:
                    result[child.tag] = child.text
                else:
                    result[child.tag] = xml_to_dict(child)
            return result
        
        data = xml_to_dict(root)
        
        # 尝试转换为DataFrame
        try:
            df = pd.json_normalize(data)
            return {'data': df, 'format': 'xml', 'raw_data': data}
        except:
            return {'data': data, 'format': 'xml', 'raw_data': data}
    
    def _import_sqlite(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """导入SQLite数据库"""
        table_name = kwargs.get('table_name')
        query = kwargs.get('query')
        
        conn = sqlite3.connect(file_path)
        
        try:
            if query:
                df = pd.read_sql_query(query, conn)
                return {'data': df, 'format': 'sqlite', 'query': query}
            elif table_name:
                df = pd.read_sql_table(table_name, conn)
                return {'data': df, 'format': 'sqlite', 'table': table_name}
            else:
                # 获取所有表
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = [row[0] for row in cursor.fetchall()]
                
                tables_data = {}
                for table in tables:
                    tables_data[table] = pd.read_sql_table(table, conn)
                
                return {'data': tables_data, 'format': 'sqlite', 'tables': tables}
        finally:
            conn.close()
    
    def _import_text(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """导入文本文件"""
        encoding = kwargs.get('encoding', 'utf-8')
        
        with open(file_path, 'r', encoding=encoding) as f:
            content = f.read()
        
        # 尝试按行分割
        lines = content.split('\n')
        df = pd.DataFrame({'content': lines})
        
        return {'data': df, 'format': 'text', 'raw_content': content}
    
    def _import_tsv(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """导入TSV文件"""
        kwargs['delimiter'] = '\t'
        return self._import_csv(file_path, **kwargs)


class UniversalDataExporter:
    """通用数据导出器"""
    
    def __init__(self):
        self.registry = FormatRegistry()
        self.export_handlers = {
            'excel': self._export_excel,
            'csv': self._export_csv,
            'json': self._export_json,
            'xml': self._export_xml,
            'sqlite': self._export_sqlite,
            'txt': self._export_text,
            'tsv': self._export_tsv
        }
    
    def export_data(self, data: Union[pd.DataFrame, Dict], 
                   file_path: str, format_type: str, **kwargs) -> bool:
        """导出数据"""
        format_info = self.registry.formats.get(format_type)
        if not format_info or not format_info.supports_export:
            raise ValueError(f"不支持的导出格式: {format_type}")
        
        handler = self.export_handlers.get(format_type)
        if not handler:
            raise ValueError(f"没有找到格式处理器: {format_type}")
        
        try:
            return handler(data, file_path, **kwargs)
        except Exception as e:
            raise Exception(f"导出文件失败 ({format_info.name}): {str(e)}")
    
    def _export_excel(self, data: Union[pd.DataFrame, Dict], 
                     file_path: str, **kwargs) -> bool:
        """导出Excel文件"""
        if isinstance(data, pd.DataFrame):
            data.to_excel(file_path, index=False, **kwargs)
        elif isinstance(data, dict):
            with pd.ExcelWriter(file_path) as writer:
                for sheet_name, df in data.items():
                    if isinstance(df, pd.DataFrame):
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
        return True
    
    def _export_csv(self, data: pd.DataFrame, file_path: str, **kwargs) -> bool:
        """导出CSV文件"""
        encoding = kwargs.get('encoding', 'utf-8')
        data.to_csv(file_path, index=False, encoding=encoding, **kwargs)
        return True
    
    def _export_json(self, data: Union[pd.DataFrame, Dict], 
                    file_path: str, **kwargs) -> bool:
        """导出JSON文件"""
        if isinstance(data, pd.DataFrame):
            json_data = data.to_dict('records')
        else:
            json_data = data
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2, default=str)
        return True
    
    def _export_xml(self, data: Union[pd.DataFrame, Dict], 
                   file_path: str, **kwargs) -> bool:
        """导出XML文件"""
        root_name = kwargs.get('root_name', 'data')
        item_name = kwargs.get('item_name', 'item')
        
        root = ET.Element(root_name)
        
        if isinstance(data, pd.DataFrame):
            for _, row in data.iterrows():
                item = ET.SubElement(root, item_name)
                for col, value in row.items():
                    elem = ET.SubElement(item, str(col))
                    elem.text = str(value)
        elif isinstance(data, dict):
            self._dict_to_xml(data, root)
        
        tree = ET.ElementTree(root)
        tree.write(file_path, encoding='utf-8', xml_declaration=True)
        return True
    
    def _export_sqlite(self, data: Union[pd.DataFrame, Dict], 
                      file_path: str, **kwargs) -> bool:
        """导出SQLite数据库"""
        table_name = kwargs.get('table_name', 'data')
        
        conn = sqlite3.connect(file_path)
        
        try:
            if isinstance(data, pd.DataFrame):
                data.to_sql(table_name, conn, if_exists='replace', index=False)
            elif isinstance(data, dict):
                for name, df in data.items():
                    if isinstance(df, pd.DataFrame):
                        df.to_sql(name, conn, if_exists='replace', index=False)
            return True
        finally:
            conn.close()
    
    def _export_text(self, data: pd.DataFrame, file_path: str, **kwargs) -> bool:
        """导出文本文件"""
        separator = kwargs.get('separator', '\n')
        
        if 'content' in data.columns:
            content = separator.join(data['content'].astype(str))
        else:
            content = data.to_string(index=False)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    
    def _export_tsv(self, data: pd.DataFrame, file_path: str, **kwargs) -> bool:
        """导出TSV文件"""
        kwargs['sep'] = '\t'
        return self._export_csv(data, file_path, **kwargs)
    
    def _dict_to_xml(self, data: Dict, parent: ET.Element):
        """字典转XML"""
        for key, value in data.items():
            elem = ET.SubElement(parent, str(key))
            if isinstance(value, dict):
                self._dict_to_xml(value, elem)
            elif isinstance(value, list):
                for item in value:
                    item_elem = ET.SubElement(elem, 'item')
                    if isinstance(item, dict):
                        self._dict_to_xml(item, item_elem)
                    else:
                        item_elem.text = str(item)
            else:
                elem.text = str(value)


class DataConverter:
    """数据格式转换器"""
    
    def __init__(self):
        self.importer = UniversalDataImporter()
        self.exporter = UniversalDataExporter()
    
    def convert_file(self, input_path: str, output_path: str, 
                    input_format: str = None, output_format: str = None, **kwargs) -> bool:
        """转换文件格式"""
        # 导入数据
        imported_data = self.importer.import_data(input_path, input_format, **kwargs)
        
        # 确定输出格式
        if not output_format:
            output_format = self.importer._detect_format(output_path)
        
        # 导出数据
        data_to_export = imported_data['data']
        return self.exporter.export_data(data_to_export, output_path, output_format, **kwargs)
    
    def batch_convert(self, file_list: List[str], output_dir: str, 
                     output_format: str, **kwargs) -> Dict[str, bool]:
        """批量转换文件"""
        results = {}
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        for input_file in file_list:
            try:
                input_path = Path(input_file)
                output_file = output_path / f"{input_path.stem}.{output_format}"
                
                success = self.convert_file(str(input_file), str(output_file), 
                                          output_format=output_format, **kwargs)
                results[input_file] = success
                
            except Exception as e:
                print(f"转换文件失败 {input_file}: {e}")
                results[input_file] = False
        
        return results


class ArchiveManager:
    """归档管理器"""
    
    def __init__(self):
        self.supported_formats = ['.zip', '.tar', '.gz']
    
    def create_archive(self, files: List[str], archive_path: str, 
                      compression: str = 'zip') -> bool:
        """创建归档文件"""
        try:
            if compression == 'zip':
                with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                    for file_path in files:
                        file_path = Path(file_path)
                        if file_path.exists():
                            zf.write(file_path, file_path.name)
            return True
        except Exception as e:
            print(f"创建归档失败: {e}")
            return False
    
    def extract_archive(self, archive_path: str, extract_to: str) -> List[str]:
        """解压归档文件"""
        extracted_files = []
        
        try:
            if archive_path.endswith('.zip'):
                with zipfile.ZipFile(archive_path, 'r') as zf:
                    zf.extractall(extract_to)
                    extracted_files = zf.namelist()
        except Exception as e:
            print(f"解压归档失败: {e}")
        
        return extracted_files


class DataValidator:
    """数据验证器"""
    
    def __init__(self):
        self.validation_rules = {}
    
    def add_validation_rule(self, column: str, rule: callable, message: str):
        """添加验证规则"""
        if column not in self.validation_rules:
            self.validation_rules[column] = []
        self.validation_rules[column].append({'rule': rule, 'message': message})
    
    def validate_data(self, data: pd.DataFrame) -> Dict[str, List[str]]:
        """验证数据"""
        errors = {}
        
        for column, rules in self.validation_rules.items():
            if column not in data.columns:
                continue
            
            column_errors = []
            for rule_info in rules:
                rule = rule_info['rule']
                message = rule_info['message']
                
                try:
                    if not data[column].apply(rule).all():
                        column_errors.append(message)
                except Exception as e:
                    column_errors.append(f"验证规则执行失败: {e}")
            
            if column_errors:
                errors[column] = column_errors
        
        return errors
    
    def validate_file_format(self, file_path: str, expected_format: str) -> bool:
        """验证文件格式"""
        try:
            registry = FormatRegistry()
            detected_format = registry.get_format_by_extension(file_path)
            return detected_format and detected_format.name.lower() == expected_format.lower()
        except:
            return False
