#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控和诊断工具
提供实时性能监控、内存使用分析和性能优化建议
"""

import time
import psutil
import gc
from functools import wraps
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: float
    memory_usage_mb: float
    memory_percent: float
    cpu_percent: float
    execution_time: float = 0.0
    function_name: str = ""
    file_size_mb: float = 0.0
    
    def __str__(self):
        return (f"[{self.function_name}] "
                f"时间: {self.execution_time:.2f}s, "
                f"内存: {self.memory_usage_mb:.1f}MB ({self.memory_percent:.1f}%), "
                f"CPU: {self.cpu_percent:.1f}%")


@dataclass
class PerformanceReport:
    """性能报告数据类"""
    total_execution_time: float = 0.0
    peak_memory_usage_mb: float = 0.0
    average_memory_usage_mb: float = 0.0
    peak_cpu_percent: float = 0.0
    average_cpu_percent: float = 0.0
    function_metrics: List[PerformanceMetrics] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    
    def add_metric(self, metric: PerformanceMetrics):
        """添加性能指标"""
        self.function_metrics.append(metric)
        self.total_execution_time += metric.execution_time
        
        # 更新峰值和平均值
        if metric.memory_usage_mb > self.peak_memory_usage_mb:
            self.peak_memory_usage_mb = metric.memory_usage_mb
        
        if metric.cpu_percent > self.peak_cpu_percent:
            self.peak_cpu_percent = metric.cpu_percent
        
        # 重新计算平均值
        if self.function_metrics:
            self.average_memory_usage_mb = sum(m.memory_usage_mb for m in self.function_metrics) / len(self.function_metrics)
            self.average_cpu_percent = sum(m.cpu_percent for m in self.function_metrics) / len(self.function_metrics)
    
    def generate_recommendations(self):
        """生成性能优化建议"""
        self.recommendations.clear()
        
        # 内存使用建议
        if self.peak_memory_usage_mb > 1000:  # 超过1GB
            self.recommendations.append("🔴 内存使用过高，建议使用分块处理大文件")
        elif self.peak_memory_usage_mb > 500:  # 超过500MB
            self.recommendations.append("🟡 内存使用较高，建议监控内存使用情况")
        
        # CPU使用建议
        if self.peak_cpu_percent > 80:
            self.recommendations.append("🔴 CPU使用率过高，建议优化算法或使用多线程")
        elif self.peak_cpu_percent > 60:
            self.recommendations.append("🟡 CPU使用率较高，建议检查是否有性能瓶颈")
        
        # 执行时间建议
        if self.total_execution_time > 60:  # 超过1分钟
            self.recommendations.append("🔴 总执行时间过长，建议优化处理流程")
        elif self.total_execution_time > 30:  # 超过30秒
            self.recommendations.append("🟡 执行时间较长，建议添加进度提示")
        
        # 函数级别建议
        slow_functions = [m for m in self.function_metrics if m.execution_time > 10]
        if slow_functions:
            func_names = [f.function_name for f in slow_functions]
            self.recommendations.append(f"🔴 以下函数执行较慢: {', '.join(func_names)}")
    
    def __str__(self):
        self.generate_recommendations()
        
        report = [
            "📊 性能报告",
            "=" * 50,
            f"总执行时间: {self.total_execution_time:.2f}秒",
            f"峰值内存使用: {self.peak_memory_usage_mb:.1f}MB",
            f"平均内存使用: {self.average_memory_usage_mb:.1f}MB",
            f"峰值CPU使用: {self.peak_cpu_percent:.1f}%",
            f"平均CPU使用: {self.average_cpu_percent:.1f}%",
            "",
            "📋 函数性能详情:",
        ]
        
        for metric in self.function_metrics:
            report.append(f"  {metric}")
        
        if self.recommendations:
            report.extend([
                "",
                "💡 优化建议:",
            ])
            for rec in self.recommendations:
                report.append(f"  {rec}")
        
        return "\n".join(report)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.report = PerformanceReport()
        self.enabled = True
        self.start_time = time.time()
    
    def enable(self):
        """启用性能监控"""
        self.enabled = True
    
    def disable(self):
        """禁用性能监控"""
        self.enabled = False
    
    def get_current_metrics(self, function_name: str = "", file_size_mb: float = 0.0) -> PerformanceMetrics:
        """获取当前性能指标"""
        memory = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent()
        
        return PerformanceMetrics(
            timestamp=time.time(),
            memory_usage_mb=memory.used / (1024 * 1024),
            memory_percent=memory.percent,
            cpu_percent=cpu_percent,
            function_name=function_name,
            file_size_mb=file_size_mb
        )
    
    def monitor_function(self, func: Callable) -> Callable:
        """函数性能监控装饰器"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not self.enabled:
                return func(*args, **kwargs)
            
            # 获取开始时的性能指标
            start_metrics = self.get_current_metrics(func.__name__)
            start_time = time.time()
            
            # 强制垃圾回收以获得更准确的内存测量
            gc.collect()
            
            try:
                # 执行函数
                result = func(*args, **kwargs)
                
                # 计算执行时间
                execution_time = time.time() - start_time
                
                # 获取结束时的性能指标
                end_metrics = self.get_current_metrics(func.__name__)
                end_metrics.execution_time = execution_time
                
                # 添加到报告
                self.report.add_metric(end_metrics)
                
                # 如果启用了实时输出，打印性能信息
                if hasattr(self, 'verbose') and self.verbose:
                    print(f"⚡ {end_metrics}")
                
                return result
                
            except Exception as e:
                # 即使出现异常也记录性能数据
                execution_time = time.time() - start_time
                error_metrics = self.get_current_metrics(f"{func.__name__}(ERROR)")
                error_metrics.execution_time = execution_time
                self.report.add_metric(error_metrics)
                raise e
        
        return wrapper
    
    def monitor_file_operation(self, file_path: str, operation_name: str = "文件操作"):
        """文件操作性能监控上下文管理器"""
        return FileOperationMonitor(self, file_path, operation_name)
    
    def get_report(self) -> PerformanceReport:
        """获取性能报告"""
        return self.report
    
    def print_report(self):
        """打印性能报告"""
        print(self.report)
    
    def save_report(self, file_path: str):
        """保存性能报告到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(str(self.report))
            print(f"✅ 性能报告已保存到: {file_path}")
        except Exception as e:
            print(f"❌ 保存性能报告失败: {e}")
    
    def reset(self):
        """重置性能监控数据"""
        self.report = PerformanceReport()
        self.start_time = time.time()


class FileOperationMonitor:
    """文件操作监控上下文管理器"""
    
    def __init__(self, monitor: PerformanceMonitor, file_path: str, operation_name: str):
        self.monitor = monitor
        self.file_path = file_path
        self.operation_name = operation_name
        self.start_time = None
        self.file_size_mb = 0.0
    
    def __enter__(self):
        if self.monitor.enabled:
            # 获取文件大小
            try:
                self.file_size_mb = Path(self.file_path).stat().st_size / (1024 * 1024)
            except:
                self.file_size_mb = 0.0
            
            self.start_time = time.time()
            print(f"📁 开始{self.operation_name}: {self.file_path} ({self.file_size_mb:.1f}MB)")
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.monitor.enabled and self.start_time:
            execution_time = time.time() - self.start_time
            metrics = self.monitor.get_current_metrics(self.operation_name, self.file_size_mb)
            metrics.execution_time = execution_time
            
            self.monitor.report.add_metric(metrics)
            print(f"✅ 完成{self.operation_name}: {execution_time:.2f}秒")


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


def monitor_performance(func: Callable) -> Callable:
    """性能监控装饰器的便捷函数"""
    return performance_monitor.monitor_function(func)


def get_performance_report() -> PerformanceReport:
    """获取全局性能报告"""
    return performance_monitor.get_report()


def print_performance_report():
    """打印全局性能报告"""
    performance_monitor.print_report()


def save_performance_report(file_path: str = "performance_report.txt"):
    """保存全局性能报告"""
    performance_monitor.save_report(file_path)


def reset_performance_monitor():
    """重置全局性能监控器"""
    performance_monitor.reset()


# 为了向后兼容，保留原有的装饰器名称
performance_monitor_decorator = monitor_performance
