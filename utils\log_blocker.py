#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志阻止器模块
通过拦截系统调用来阻止log文件夹的创建
"""

import os
import sys
from pathlib import Path


# 保存原始函数
_original_mkdir = os.mkdir
_original_makedirs = os.makedirs


def _blocked_mkdir(path, mode=0o777, *, dir_fd=None):
    """拦截mkdir调用，阻止创建log相关目录"""
    path_str = str(path).lower()
    
    # 检查是否为log相关目录
    blocked_names = ['log', 'logs', '.log', '.logs', 'debug', 'temp', 'tmp']
    
    # 获取目录名
    dir_name = Path(path).name.lower()
    
    if dir_name in blocked_names:
        print(f"🚫 阻止创建目录: {path}")
        # 不创建目录，直接返回
        return
    
    # 允许创建其他目录
    return _original_mkdir(path, mode, dir_fd=dir_fd)


def _blocked_makedirs(name, mode=0o777, exist_ok=False):
    """拦截makedirs调用，阻止创建log相关目录"""
    path_str = str(name).lower()
    
    # 检查路径中是否包含log相关目录
    blocked_names = ['log', 'logs', '.log', '.logs', 'debug', 'temp', 'tmp']
    
    path_parts = Path(name).parts
    for part in path_parts:
        if part.lower() in blocked_names:
            print(f"🚫 阻止创建目录树: {name}")
            # 不创建目录，直接返回
            return
    
    # 允许创建其他目录
    return _original_makedirs(name, mode, exist_ok)


def install_log_blocker():
    """安装日志阻止器"""
    print("🛡️ 安装日志目录创建阻止器...")
    
    # 替换os模块的函数
    os.mkdir = _blocked_mkdir
    os.makedirs = _blocked_makedirs
    
    print("✅ 日志阻止器安装完成")


def uninstall_log_blocker():
    """卸载日志阻止器"""
    print("🔄 卸载日志目录创建阻止器...")
    
    # 恢复原始函数
    os.mkdir = _original_mkdir
    os.makedirs = _original_makedirs
    
    print("✅ 日志阻止器卸载完成")


def patch_third_party_logging():
    """修补第三方库的日志创建行为"""
    
    # 1. 修补logging模块
    try:
        import logging
        import logging.handlers
        
        # 禁用文件处理器
        original_file_handler_init = logging.FileHandler.__init__
        
        def blocked_file_handler_init(self, filename, mode='a', encoding=None, delay=False, errors=None):
            # 检查文件名是否包含log
            if 'log' in str(filename).lower():
                print(f"🚫 阻止创建日志文件: {filename}")
                # 使用NullHandler替代
                logging.NullHandler.__init__(self)
                return
            return original_file_handler_init(self, filename, mode, encoding, delay, errors)
        
        logging.FileHandler.__init__ = blocked_file_handler_init
        
    except Exception:
        pass
    
    # 2. 修补可能的第三方库
    try:
        # 如果pandas被导入，修补其日志行为
        if 'pandas' in sys.modules:
            import pandas as pd
            # 禁用pandas的所有警告输出到文件
            pd.options.mode.chained_assignment = None
            
    except Exception:
        pass
    
    # 3. 修补openpyxl
    try:
        if 'openpyxl' in sys.modules:
            import warnings
            warnings.filterwarnings('ignore', module='openpyxl')
            
    except Exception:
        pass


def setup_comprehensive_log_blocking():
    """设置全面的日志阻止"""
    print("🛡️ 设置全面的日志阻止系统...")
    
    # 1. 安装系统级阻止器
    install_log_blocker()
    
    # 2. 修补第三方库
    patch_third_party_logging()
    
    # 3. 设置环境变量
    env_vars = {
        'PYTHONDONTWRITEBYTECODE': '1',
        'PYTHONIOENCODING': 'utf-8',
        'QT_LOGGING_RULES': '*.debug=false;qt.qpa.*=false;*.info=false',
        'QT_LOGGING_TO_CONSOLE': '0',
        'PYINSTALLER_SUPPRESS_WARNINGS': '1',
        'OPENPYXL_DISABLE_WARNINGS': '1',
        'PANDAS_DISABLE_WARNINGS': '1'
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
    
    # 4. 重定向可能的日志输出
    try:
        import io
        
        # 创建一个空的输出流
        null_stream = io.StringIO()
        
        # 如果有模块尝试写入日志，重定向到空流
        if hasattr(sys, 'stderr'):
            # 保存原始stderr，但在某些情况下重定向
            pass
            
    except Exception:
        pass
    
    print("✅ 全面日志阻止系统设置完成")


def cleanup_log_blocking():
    """清理日志阻止系统"""
    print("🧹 清理日志阻止系统...")
    
    # 卸载阻止器
    uninstall_log_blocker()
    
    print("✅ 日志阻止系统清理完成")


# 程序退出时自动清理
import atexit
atexit.register(cleanup_log_blocking)
